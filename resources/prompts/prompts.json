{"ebook_plan": "I am writing an ebook with a total of :words words. The ebook will consist of chapters, and each chapter will have multiple sections. Each section will contain multiple key points. Guidelines:\n1. Each chapter must be minimum 900 words and maximum is unlimited but need to be realistic, and the total chapters should realistically divide the total words without making chapters too short or too long.\n2. Each section must be minimum 300 words and maximum is unlimited but need to be realistic. Sections should provide enough depth; avoid overly brief sections.\n3. Key points should not be too brief; they should convey meaningful information. Provide a JSON structure that distributes the words properly to create a balanced ebook. The response should only return a valid JSON object, following this structure:\n{\n  \"total_words\": :words,\n  \"total_chapters\": \"minimum to maximum\",\n  \"words_per_chapter_range\": \"minimum to maximum\",\n  \"sections_per_chapter_range\": \"minimum to maximum\",\n  \"words_per_section_range\": \"minimum to maximum\",\n  \"key_points_per_section_range\": \"minimum to maximum\",\n  \"words_per_key_point_range\": \"minimum to maximum\"\n}\nEnsure minimum and maximum difference shouldn't be more than 30% and Total chapters will be realistic and standard.", "ebook_title": "I’m writing an eBook and I need you to generate a title for my eBook based on the following parameters:\n\nTopic of eBook: :topic\nEbook Context: :context\nPurpose of ebook: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\nThe output **must** be in valid JSON format and **must** include only the title, wrapped in HTML `<h1>` tags. The key should be 'title'. Example output: {\"title\": \"<h1>eBook Title</h1>\"}.\n\nMake sure the title is descriptive, compelling, and relevant to the provided parameters.", "content_summary": "I need you to summarized this given content within 200 words. Content summary will carefully cover the main key facts of the contents.\n\nContent: :content \n\nThe output **must** be within 200 words.\n\nThe output **must** be in valid JSON format and **must** include only the summary in plaintext format. **Your response will be only json no additional text.** Example output: {\"summary\": \"content summary in plain text\"}. ", "content_summary_by_url": "Summarize the content from the provided URL within 200 words, capturing only the main key facts in a concise and direct manner. Visit and analyze the webpage to generate the summary. Avoid meta-commentary like 'the content discusses' and use a natural, plain writing style.\n\nURL: :url\n\nThe output **must** be within 200 words.\nThe output **must** be in valid JSON format, containing only the summary in plaintext. **Your response must be JSON only, with no additional text.** Example output: {\"summary\": \"Summary of key facts in plain text\"}.", "ebook_chapters": "I need you to write detailed and well-structured chapters with an intro for my eBook based on the following parameters:\n\nTotal chapters: Total chapter of the ebook will be :total_chapters and each chapter should've :words_per_chapter_range words \nTopic of eBook: :topic\nEbook Context: :context\nTitle of eBook: :title\nPurpose of eBook: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\nThe output must be in valid JSON format and must include both the chapter title and the intro text. Each chapter title should be wrapped in HTML `<h2>` tags, and the chapter intro should contain appropriate HTML tags (such as `<p>`, `<strong>`, `<em>`, etc.) as needed for structure and emphasis. **Only return a valid JSON response**, following this structure:\n\nExample output: [{\"chapter_title\": \"<h2>Chapter 1 Title</h2>\", \"chapter_intro\": \"<p>Intro text for Chapter 1 with <strong>emphasis</strong></p>\"}, {\"chapter_title\": \"<h2>Chapter 2 Title</h2>\", \"chapter_intro\": \"<p>Intro text for Chapter 2 with <em>some formatting</em></p>\"}]\n\n You must ensure total chapter will be :total_chapters and you also ensure that the chapters are relevant, engaging, and aligned with the parameters provided.", "ebook_sections": "I need you to write detailed, well-structured, and high-quality sections and key points for an eBook chapter based on the following parameters:\n\nSection Length: Each chapter will include :sections_per_chapter_range sections. Each section will contain :key_points_per_section_range key points, with a total word count of :words_per_section_range. Within this words 10% to 20% of the section's word count will be dedicated to the introduction.Topic of eBook: :topic\nEbook Context: :context\nTitle of eBook: :title\nChapter Title: :chapter_title\nChapter Intro: :chapter_intro\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\nYour output will be only in valid JSON format with section titles, intro, and key points in HTML. **Your response will be only json no additional text.** Example output:\n[\n  {\n    \"section_title\": \"<h3>1.1 Section title</h3>\",\n    \"section_intro\": \"<p>Section intro</p>\",\n    \"key_points\": [\n      \"<h4>Key point 1</h4>\",\n      \"<h4>Key point 2</h4>\"\n    ]\n  },\n  {\n    \"section_title\": \"<h3>1.2 Another Section title</h3>\",\n    \"section_intro\": \"<p>Another section intro</p>\",\n    \"key_points\": [\n      \"<h4>Another key point 1</h4>\",\n      \"<h4>Another key point 2</h4>\"\n    ]\n  }\n]\n", "section_keypoint_content": "I need you to generate detailed, well-structured, and high-quality content for the key points in a section of my eBook chapter based on the following parameters:\n\nTotal words of each key point: Words per key point content will be around :words_per_key_point_range words \nTopic of eBook: :topic\nEbook Context: :context\nTitle of eBook: :title\nChapter Title: :chapter_title\nChapter Intro: :chapter_intro\nSection Title: :section_title\nSection Intro: :section_intro\nSection Key Points: :key_points\nPurpose: :purpose\nTargeted Audience: :audience\nTone & Style: :tone\nLanguage: :language\n\nThe output must be in valid HTML format, with each key point followed by its detailed content. Use appropriate HTML tags (such as <h4>, <p>, <strong>, <em>) to structure the content properly. **Your response will be only json no additional text.** \n\nExample output:\n\n<h4>Key Point 1</h4>\n<p>Here is content that will go <strong>here</strong>. Make sure the content is meaningful and informative.</p>\n\n<h4>Key Point 2</h4>\n<p>Another content block <em>goes here</em>. Provide clear, concise, and relevant information for each key point.</p>\n\nMake sure the content aligns with the provided parameters and is engaging, informative, and relevant. Only return valid HTML output following the above structure. And you should strict with the length and word count while generating content.", "image_generation": "Create an image based on the following details:\n\nTopic: :topic\nChapter Title: :chapter_title\nImage: :image_style\nSection Title: :section_title\n\nThe image should visually represent the essence of the topic, chapter, and section provided. After generating the image, return only the image URL in a JSON format with the key 'image_url'.\n\nExample output: {\"image_url\": \"https://example.com/path/to/generated/image.jpg\"}\n\nEnsure the image aligns with the provided details and return only the URL in JSON format.\n\n Ensure that the image is purely visual with no text, letters, numbers, or symbols present anywhere.", "cover_image": "Create a :page_size size ebook cover image based on following details: \n\nEbook name: :title\nImage size: total image size will be :page_size size\n\nThe cover image should visually represent the essence of the ebook name and not contain any text in the image. After generating the image, return only the image URL in a JSON format with the key 'image_url'.\n\nExample output: {\"image_url\": \"https://example.com/path/to/generated/image.jpg\"}\n\nEnsure the image aligns with the provided details and return only the URL in JSON format.", "book_title_generation": "Generate a list of potential book titles for a book about :topic. The book should appeal to :audience and be in the :genre genre. The tone should be :tone and focus on :key_aspect . Your output **must** be like example output \n\nExample output: {\"book_title\": \"Book title 1\", \"book_title\": \"Book title 2\"}", "best_keyword_categories": "Suggest the best keywords and categories for a book about :topic. Consider :audience audience, :ebook_publishing_platform categories, search trends, and competitive advantage. You'll generate a total of :limit results.\n\nYour output **must** be in the following JSON format:\n\n{\n  \"best_keyword_categories\": [\n    \"Keyword 1 (keyword)\",\n    \"Category 1 (category)\",\n    \"Keyword 2 (keyword)\",\n    \"Category 2 (category)\",\n    \"Keyword 3 (keyword)\",\n    \"Category 3 (category)\"\n  ]\n}", "popular_books_in_niche": "What are the top best-selling books in the :niche category? Provide details such as author, summary, and key takeaways. Consider recent trends and high-rated books on platforms like :platform.\n\nAlso consider:\nPublishing year range: :publishing_year_range months\nTargeted market: :target_market\nSuccess metrics: :success_metrics\n\nYou'll generate a total of :limit results.\n\nYour output **must** be in the following JSON format and will not include any other things:\n\n[\n  {\n    \"author\": \"Author name\",\n    \"summary\": \"details summary\",\n    \"key_takeways\": [\n      \"Key take way 1\",\n      \"Key take way 2\",\n      \"Key take way 3\"\n    ]\n  },\n  {\n    \"author\": \"Author name\",\n    \"summary\": \"details summary\",\n    \"key_takeways\": [\n      \"Key take way 1\",\n      \"Key take way 2\",\n      \"Key take way 3\"\n    ]\n  }\n]", "author_bio": "Write a professional author bio for author :author_name, who specializes in :speciality. Their notable works include :notable_works. Professional background of the author is :professional_background. The tone should be :tone.\n\nYour output **must** be like example JSON output \n\nExample output: {\"author_bio\": \"author bio details\"}\"", "ignore_terms": "\nAvoid overly formal or AI-sounding words like 'delve,' 'unveil,' or 'embark.' Use natural, human-like language instead.\n", "random_ebook_title": "Generate a list of 10 random book titles for random niche and topic. Your output **must** be like example output \n\nExample output: {\"book_title\": \"Book title 1\", \"book_title\": \"Book title 2\"}", "random_ebook_details": "My ebook title is :title \n\nPlease generate the following details:\n\n- A context of approximately 200 words (plain text).\n- A KDP-friendly description.\n- An author name.\n- Relevant keywords (up to 10 keywords, separated by commas).\n\nPlease format the response as follows:\n\n[\n    {\n        \"title\": \":title\",\n        \"context\": \"Generated context text here...\",\n        \"description\": \"Generated KDP-friendly description here...\",\n        \"author_name\": \"Generated author name here...\",\n        \"keyword\": \"Keyword 1, Keyword 2, Keyword 3, ...\"\n    }\n]"}