<style>
    .prev {
        background: red;
        display: inline-block;
        position: absolute;
        top: 44%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        padding: 8px;
        border-radius: 50%;
        z-index: 6;
        left: 5px;
        cursor: pointer;
        background: #fff;
        box-shadow: 0 3px 6px #1b1b1b29
    }

    .disabled {
        background: #eee;
    }

    .download-button {
        text-align: center;
        cursor: pointer;
        width: 100%;
    }

    .next {
        background: red;
        display: inline-block;
        position: absolute;
        top: 44%;
        -webkit-transform: translateY(-50%);
        transform: translateY(-50%);
        padding: 8px;
        border-radius: 50%;
        z-index: 6;
        right: 5px;
        cursor: pointer;
        background: #fff;
        box-shadow: 0 3px 6px #1b1b1b29
    }
</style>
@php
    foreach($templates as $template){
        $template->image_url = $template->image ? Storage::drive('s3')->temporaryUrl($template->image, now()->addMinutes(15)) : null;

    }
    foreach($images as $image){
        $image->image_url = $image->src ? Storage::drive('s3')->temporaryUrl($image->src, now()->addMinutes(15)) : null;

    }
    // $initialBackgroundImageUrl = $background_image ?? '';
    $initialBackgroundImageUrl = '';

@endphp

<div x-data="{
    showModal: false,
    showBackgroundModal: false,
    backgroundImage: @js($initialBackgroundImageUrl),
    templates: {{ Js::from($templates) }},
    images: {{ Js::from($images) }},
    currentPage: 0,
    perPage: window.screen.width > 500 ? 4 : 1
}">
    <div class="flex">
        <span class="">
            Configure Ebook Options:
        </span>
        <button style="margin-left: 40px;
        padding: 5px 28px; background-color: #ea580c; color: #ffffff;"
            @click.prevent="showModal = true" class="py-2 text-white bg-orange-600 rounded hover:bg-orange-700">
            Select layout
        </button>
        <button style="margin-left: 40px;
        padding: 5px 28px; background-color: #ea580c; color: #ffffff;"
            @click.prevent="showBackgroundModal = true" class="py-2 text-white bg-orange-600 rounded hover:bg-orange-700">
            Change Background image
        </button>


    </div>
    {{-- <img style="width:80px;" :src="backgroundImage" alt=""> --}}


    <div x-show="showModal" x-transition
    class="fixed inset-0 z-50 px-4 py-16 overflow-y-auto bg-gray-900 bg-opacity-50 backdrop-blur-sm"
    style="display: none;">

    <div class="relative w-full p-6 mx-auto bg-white border border-gray-200 shadow-2xl rounded-xl max-w-7xl">

        <!-- Header -->
        <div class="flex items-center justify-between pb-4 mb-6 border-b">
            <h1 class="mx-auto text-2xl font-bold text-gray-800">Choose the formatting design that you like the best</h1>
            <button @click.prevent="showModal = false"
                class="text-3xl leading-none text-gray-500 transition top-6 right-6 hover:text-red-500"
                aria-label="Close">
                &times;
            </button>
        </div>

        <!-- Templates -->
        <div class="grid grid-cols-1 gap-6 sm:grid-cols-2 md:grid-cols-3">
            <template x-for="template in templates.slice(currentPage * perPage, (currentPage * perPage) + perPage)"
                :key="template.id">
                <div @click.prevent="showModal = false"
                    wire:click="setDesign(template.id)"
                    class="p-4 overflow-hidden transition-all duration-200 bg-white border border-gray-300 shadow cursor-pointer rounded-xl hover:shadow-xl"
                    :style="{
                        backgroundImage: template.image_url ? `url('${template.image_url}')` : 'none',
                        backgroundSize: 'cover',
                        backgroundPosition: 'center',
                        fontFamily: template.font,
                        fontSize: `${template.font_size}px`,
                        lineHeight: template.line_space,
                        textAlign: template.text_align,
                        padding: `${template.margin_top || 20}px ${template.margin_right || 20}px ${template.margin_bottom || 20}px ${template.margin_left || 20}px`
                    }">

                    <h3 class="mb-1 text-lg font-bold text-center"
                        :style="{ fontSize: `${template.heading}px` }">
                        Chapter 6: Planting Your Tree
                    </h3>
                    <h4 class="mb-2 text-sm text-center text-gray-500"
                        :style="{ fontSize: `${template.sub_heading}px` }">
                        Digging the Hole
                    </h4>
                    <p class="leading-snug text-gray-700" :style="{ marginBottom: `${template.paragraph_space}px` }">
                        As we move to the dense forest, a spectacle of nature's ingenuity unfolds before us...
                        The hole must be as wide as the plant's root ball, allowing for ample growth...
                    </p>
                    <p class="leading-snug text-gray-700" :style="{ marginBottom: `${template.paragraph_space}px` }">
                        In the soft, moist earth, the beetle begins its task. With its hind legs, it shovels the soil,
                        flinging it in miniature arcs that scatter in the undergrowth...
                    </p>
                    <p class="leading-snug text-gray-700" :style="{ marginBottom: `${template.paragraph_space}px` }">
                        The process is painstaking, the progress slow. Yet, the beetle does not falter...
                        a cradle and a larder, a
                    </p>
                    <p class="mt-2 text-xs text-right text-gray-500">Page 36</p>
                </div>
            </template>
        </div>

        <!-- Pagination Controls -->
        {{-- <div class="flex items-center justify-between px-4 mt-8">
            <button type="button"
                x-on:click="currentPage = currentPage > 0 ? currentPage - 1 : 0"
                class="flex items-center space-x-2 text-gray-600 hover:text-blue-600 disabled:opacity-40"
                :disabled="currentPage === 0">
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path d="M10.828 12l4.95 4.95-1.414 1.414L8 12l6.364-6.364 1.414 1.414z"/>
                </svg>
                <span>Previous</span>
            </button>

            <button type="button"
                x-on:click="currentPage = currentPage < (Math.ceil(templates.length / perPage) - 1) ? currentPage + 1 : currentPage"
                class="flex items-center space-x-2 text-gray-600 hover:text-blue-600"
                :disabled="currentPage >= (Math.ceil(templates.length / perPage) - 1)">
                <span>Next</span>
                <svg xmlns="http://www.w3.org/2000/svg" class="w-5 h-5" viewBox="0 0 24 24">
                    <path fill="none" d="M0 0h24v24H0z"></path>
                    <path d="M13.172 12l-4.95-4.95 1.414-1.414L16 12l-6.364 6.364-1.414-1.414z"/>
                </svg>
            </button>
        </div> --}}

        <!-- Footer -->
        <div class="flex justify-end mt-6">
            <button @click.prevent="showModal = false"
                class="px-5 py-2 text-white transition bg-gray-700 rounded-lg hover:bg-gray-800">Close</button>
        </div>

    </div>
</div>

    <div x-show="showBackgroundModal" x-transition
    class="fixed inset-0 z-50 px-4 py-16 overflow-auto bg-gray-900 bg-opacity-50 backdrop-blur-sm"
    style="display: none;">

    <div class="relative w-full max-w-5xl p-8 mx-auto bg-white border border-gray-200 shadow-2xl rounded-xl">

        <!-- Header -->
        <div class="flex items-center justify-between pb-4 mb-6 border-b">
            <h2 class="text-xl font-bold text-gray-800">🎨 Choose the Background Image</h2>
            <button @click.prevent="showBackgroundModal = false"
                class="text-3xl leading-none text-gray-500 transition hover:text-red-500 focus:outline-none"
                aria-label="Close">
                &times;
            </button>
        </div>

        <!-- Image Grid -->
        <div class="grid grid-cols-2 gap-6 sm:grid-cols-3 md:grid-cols-4">
            <!-- No Image -->
            <div @click.prevent="showBackgroundModal = false"
                wire:click="setBackgroundImage('')"
                class="flex flex-col overflow-hidden transition-all duration-200 bg-white border border-gray-100 shadow cursor-pointer rounded-xl hover:shadow-xl hover:border-blue-400">
                <div class="p-0 overflow-hidden" style="height: 180px;">
                    <div class="flex items-center justify-center w-full h-full bg-gray-100 border border-gray-300 border-dashed rounded-t-lg">
                        <span class="font-semibold text-gray-700">No Image</span>
                    </div>
                </div>
                <div class="p-1 text-xs text-center text-gray-600 border-t border-gray-100 bg-gray-50">Default</div>
            </div>

            <!-- Images -->
            <template x-for="image in images" :key="image.id">
                <div @click.prevent="showBackgroundModal = false; backgroundImage = image.image_url"
                    wire:click="setBackgroundImage(image.src)"
                    class="flex flex-col overflow-hidden transition-all duration-200 bg-white border border-gray-100 shadow cursor-pointer rounded-xl hover:shadow-xl hover:border-blue-400">
                    <div class="p-0 overflow-hidden" style="height: 180px;">
                        <div :style="'background-image: url(' + image.image_url + '); background-size: contain; background-position: center center; width: 100%; height: 100%;background-repeat: no-repeat;'" class="w-full h-full hover:scale-[1.02] transition-transform duration-200 rounded-t-lg"></div>
                    </div>
                    <div class="p-1 text-xs text-center text-gray-600 border-t border-gray-100 bg-gray-50" x-text="'Background ' + image.id"></div>
                </div>
            </template>
        </div>

        <!-- Footer -->
        <div class="flex justify-end mt-8">
            <button @click.prevent="showBackgroundModal = false"
                class="px-5 py-2 text-white transition bg-gray-700 rounded-lg hover:bg-gray-800">Close</button>
        </div>
    </div>
</div>


</div>
