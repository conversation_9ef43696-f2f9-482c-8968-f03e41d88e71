<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ strip_tags($ebook->title) }}</title>
    <style>
        body {
            font-family: {{ $format->font }};
            line-height: {{ $format->line_space }};
            margin: 0;
            padding: 0;
            font-size: {{ $format->font_size }}px;
        }
        .chapter {
            margin-top: 2em;
            @php
                $margins = json_decode($format->margins ?? '{"top":20,"bottom":20,"left":20,"right":20}', true);
            @endphp
            padding: {{ $margins['top'] ?? 20 }}px {{ $margins['right'] ?? 20 }}px {{ $margins['bottom'] ?? 20 }}px {{ $margins['left'] ?? 20 }}px;
        }
        h1, h2, h3 {
            text-align: {{ $format->text_align }};
        }
        p {
            text-align: {{ $format->text_align }};
            margin: {{ $format->paragraph_space }}px 0;
        }

        .ebook-title {
            text-align: {{ $format->text_align }};
            padding-top: 40%;
        }
        .ebook-title h1 {
            font-size: {{ $format->heading }};
        }
        .ebook-title h4 {
            font-size: {{ $format->sub_heading }};
            font-weight: normal;
            line-height: {{ $format->line_space }};
        }
        .toc {
            margin: 2em 0;
            padding: 0 1em;
        }
        .toc h1 {
            text-align: center;
            margin-bottom: 1em;
        }
        .toc a {
            text-decoration: none;
            color: inherit;
        }
        .toc a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>

{{--@php--}}
{{--    $coverImageData = '';--}}
{{--    if ($ebook->cover_image) {--}}
{{--        $coverPath = storage_path('app/public/' . $ebook->cover_image);--}}
{{--        if (file_exists($coverPath)) {--}}
{{--            $mimeType = mime_content_type($coverPath) ?: 'image/jpeg';--}}
{{--            $fileContents = file_get_contents($coverPath);--}}
{{--            $base64 = base64_encode($fileContents);--}}
{{--            $coverImageData = "data:{$mimeType};base64,{$base64}";--}}
{{--        }--}}
{{--    }--}}
{{--@endphp--}}


<!-- Title Page -->
{{--<div class="ebook-title">--}}
{{--    <h1>{{ strip_tags($ebook->title) }}</h1>--}}
{{--    <h4>Author: {{ $ebook->author ?? $ebook->user->name }}</h4>--}}
{{--</div>--}}

<!-- Table of Contents -->
<div class="toc">
    <h1>Table of Contents</h1>
    @foreach($ebook->chapters as $index => $chapter)
        <h2>
            <!-- Strip tags so we don't see raw HTML in the TOC -->
            <a href="#chapter{{ $index }}">{{ strip_tags($chapter->title) }}</a>
        </h2>
        @foreach($chapter->sections as $sectionIndex => $section)
            <h3>
                <a href="#chapter{{ $index }}section{{ $sectionIndex }}">{{ strip_tags($section->title) }}</a>
            </h3>
        @endforeach
    @endforeach
</div>

<!-- Chapters -->
@foreach($ebook->chapters as $index => $chapter)
    <!-- Use an ID so the TOC links jump here -->
    <div class="chapter" id="chapter{{ $index }}"
    @if($bgImage)
    style="
        background-image: linear-gradient(rgba(255, 255, 255, {{ ($format->background_opacity ?? 70) / 100 }}), rgba(255, 255, 255, {{ ($format->background_opacity ?? 70) / 100 }})), url('{{ $bgImage }}');
        background-repeat: no-repeat;
        background-size: cover;
        background-blend-mode: normal;
        background-attachment: fixed;
    "
@endif

>
        <!-- Render the HTML stored in $chapter->title -->
        <h2>{!! strip_tags($chapter->title) !!}</h2>
{{--        <p>{!! $chapter->intro !!}</p>--}}
        {!! linkifyContent($chapter->intro ?? '', $ebook) !!}

        @foreach($chapter->sections as $sectionIndex => $section)
            @php
                $cleanSectionTitle = preg_replace('/^\d+(\.\d+)*\s*/', '', strip_tags($section->title));
            @endphp

            <h3 id="chapter{{ $index }}section{{ $sectionIndex }}">{{ $cleanSectionTitle }}</h3>

            {{--            <p>{!! $section->intro !!}</p>--}}
            {!! linkifyContent($section->intro ?? '', $ebook) !!}
            @if($section->getMeta('image_source'))
                <img src="{{ $section->getMeta('image_source') }}" alt="{{ strip_tags($section->title) }}" />
            @endif
{{--            <p>{!! $section->body ?? '' !!}</p>--}}
            {!! linkifyContent($section->body ?? '', $ebook) !!}

            @if($ebook->getForm('affiliate_link'))
                <div style="
                    width: 100%;
                    text-align: center;
                    background-color: #f2f2f2;
                    font-size: 14px;
                    padding: 10px;
                    z-index: 9999;
                    margin-top: 10px;
                    margin-bottom: 10px;
                ">
                    <a href="{{ $ebook->getForm('affiliate_link_url') }}"
                       style="color: #000;">{{ $ebook->getForm('affiliate_link_keyword') }}</a>
                </div>
            @endif
        @endforeach
    </div>
@endforeach

</body>
</html>
