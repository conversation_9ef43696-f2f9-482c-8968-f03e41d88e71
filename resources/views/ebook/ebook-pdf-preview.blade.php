<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{ strip_tags($ebook->title) }}</title>
    <style>
        body {
            font-family:
                {{$format->font}}
            ;
            line-height:
                {{$format->line_space}}
            ;
            margin: 0;
            padding: 0;
            font-size:
                {{$format->font_size}}
                px
        }

        h1,
        h2,
        h3,
        h4 {
            text-align:
                {{$format->text_align}}
            ;
        }

        p {
            text-align:
                {{$format->text_align}}
            ;
            margin:
                {{$format->paragraph_space}}
                px 0;
        }

        .chapter {
            page-break-before: always;
        }

        /* Centering the title page */
        .ebook-title {
            text-align:
                {{$format->text_align}}
            ;
            padding-top: 40%;
        }

        .ebook-title h1 {
            font-size:
                {{$format->heading}}
            ;
            text-align:
                {{$format->text_align}}
            ;
        }

        .ebook-title h4 {
            font-size:
                {{$format->sub_heading}}
            ;
            font-weight: normal;
            line-height:
                {{$format->line_space}}
            ;
            text-align:
                {{$format->text_align}}
            ;
        }

        ul {
            padding-left: 0;
        }

        li {
            list-style-type: none;
            font-weight: bold;
            font-size: 16px;
            line-height:
                {{$format->line_space}}
            ;
        }
    </style>
</head>

<body>
    @php
        $bg_image = "";
        if ($format->background_image) {
            $bg_image = Storage::disk('s3')->temporaryUrl($format->background_image, now()->addMinutes(30));
        }
    @endphp
    {{--<div style="">--}}
        {{-- <div style="--}}
{{--        position: absolute;--}}
{{--        top: 0;--}}
{{--        left: 0;--}}
{{--        right: 0;--}}
{{--        bottom: 0;--}}
{{--        width: 100%;--}}
{{--        height: 100%;--}}
{{--        background-image: url('{{ public_path('storage/' . $ebook->cover_image) }}');--}}
{{--        background-size: 100% 100%;--}}
{{--        background-position: center;--}}
{{--        background-repeat: no-repeat;--}}
{{--        margin: 0;--}}
{{--        padding: 0;">--}}
            {{-- </div>--}}
        {{--</div>--}}






    <!-- Title Page -->
    <div class="ebook-title">
        <h1>{{ strip_tags($ebook->title) }}</h1>
        <h4>Author: {{ $ebook->author ?? $ebook->user->name }}</h4>
    </div>

    <!-- TOC Page Break -->
    <tocpagebreak toc-preHTML="&lt;H2&gt;Table of Contents&lt;/H2&gt;" toc-postHTML="" links="ON" />
    <!-- Chapters -->
    @foreach ($ebook->chapters as $index => $chapter)
        <!-- Conditionally apply the 'chapter' class to all except the first one -->
        <div class="{{ $index === 0 ? '' : 'chapter' }}" @if($bg_image)
        style="background-image: url({{ $bg_image }});background-size: cover;" @endif>
            <!-- TOC Entry for the Chapter -->
            {{--
            <tocentry content="{{ strip_tags($chapter->title) }}" level="1" /> --}}

            <h2>{!! $chapter->title !!}</h2>
            <p>{!! $chapter->intro !!}</p>

            <!-- Sections -->
            @foreach ($chapter->sections as $sectionIndex => $section)
                {{--
                <tocentry content="{{ strip_tags($section->title) }}" level="2" /> --}}

                <h3>{!! $section->title !!}</h3>
                <p>{!! $section->intro !!}</p>

                <!-- Key Points -->
                <p>{!! $section->body ?? '' !!}</p>
            @endforeach
        </div>
    @endforeach


</body>

</html>