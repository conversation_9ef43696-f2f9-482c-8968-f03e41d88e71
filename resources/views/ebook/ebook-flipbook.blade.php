<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="csrf-token" content="{{ csrf_token() }}">
    <title>{{ strip_tags($ebook->campaign->title) ?? 'Ebook Flipbook' }} - Flipbook</title>
    <script src="https://cdn.3dflipbook.net/js/jquery.min.js"></script>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        /* Review Modal Styles */
        .review-modal {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.7);
            z-index: 1000;
            align-items: center;
            justify-content: center;
        }

        .review-modal-content {
            background-color: white;
            padding: 30px;
            border-radius: 10px;
            width: 90%;
            max-width: 500px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
            position: relative;
        }

        .close-modal {
            position: absolute;
            top: 10px;
            right: 15px;
            font-size: 24px;
            cursor: pointer;
            color: #666;
        }

        .close-modal:hover {
            color: #000;
        }

        .rating-container {
            margin: 20px 0;
        }

        .star-rating {
            display: flex;
            flex-direction: column;
            align-items: center;
        }

        .stars {
            display: flex;
            gap: 5px;
            margin-bottom: 10px;
        }

        .star {
            font-size: 30px;
            color: #ddd;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .star.active {
            color: #FFD700; /* Gold color for active stars */
        }

        .star:hover {
            color: #FFED85; /* Light gold for hover effect */
        }

        .rating-text {
            font-size: 16px;
            font-weight: bold;
            color: #555;
            margin-top: 5px;
        }

        .submit-review {
            background-color: #4CAF50;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            transition: background-color 0.3s;
        }

        .submit-review:hover {
            background-color: #45a049;
        }

        .thank-you-message {
            display: none;
            text-align: center;
            padding: 20px;
        }
    </style>
</head>
<body>

<div class="sample-container-box">
    <div class="sample-container" style="width: 100%; height: 100%; position: absolute;">

    </div>
</div>

<!-- Review Modal -->
<div id="reviewModal" class="review-modal">
    <div class="review-modal-content">
        <span class="close-modal">&times;</span>
        <div id="reviewForm">
            @php
                $config = $ebook->campaign->getReviewFormConfig();
            @endphp
            <h2>{{ $config['title'] }}</h2>
            <p>{{ $config['subtitle'] }}</p>

            <form id="ebookReviewForm">
                <input type="hidden" name="campaign_id" value="{{ $ebook->campaign->id }}">

                @if($ebook->campaign->shouldShowReviewField('name'))
                <div class="mb-3">
                    <label for="name" class="form-label">Your Name</label>
                    <input type="text" class="form-control" id="name" name="name"
                        {{ $ebook->campaign->isReviewFieldRequired('name') ? 'required' : '' }}>
                </div>
                @endif

                @if($ebook->campaign->shouldShowReviewField('email'))
                <div class="mb-3">
                    <label for="email" class="form-label">Your Email</label>
                    <input type="email" class="form-control" id="email" name="email"
                        {{ $ebook->campaign->isReviewFieldRequired('email') ? 'required' : '' }}>
                </div>
                @endif

                @if($ebook->campaign->shouldShowReviewField('rating'))
                <div class="mb-3 rating-container">
                    <label class="form-label">Your Rating</label>
                    <div class="star-rating">
                        <input type="hidden" id="rating" name="rating" value="5"
                            {{ $ebook->campaign->isReviewFieldRequired('rating') ? 'required' : '' }}>
                        <div class="stars">
                            <i class="star fas fa-star" data-rating="1"></i>
                            <i class="star fas fa-star" data-rating="2"></i>
                            <i class="star fas fa-star" data-rating="3"></i>
                            <i class="star fas fa-star" data-rating="4"></i>
                            <i class="star fas fa-star" data-rating="5"></i>
                        </div>
                        <div class="rating-text">Excellent (5/5)</div>
                    </div>
                </div>
                @endif

                @if($ebook->campaign->shouldShowReviewField('comment'))
                <div class="mb-3">
                    <label for="comment" class="form-label">Comments {{ $ebook->campaign->isReviewFieldRequired('comment') ? '' : '(Optional)' }}</label>
                    <textarea class="form-control" id="comment" name="comment" rows="3"
                        {{ $ebook->campaign->isReviewFieldRequired('comment') ? 'required' : '' }}></textarea>
                </div>
                @endif

                <button type="submit" class="submit-review">{{ $config['submit_button_text'] }}</button>
            </form>
        </div>

        <div id="thankYouMessage" class="thank-you-message">
            <h2>{{ $config['thank_you_message'] }}</h2>
            <p>Your feedback has been submitted successfully.</p>
            <button type="button" class="btn btn-primary mt-3 close-thank-you">{{ $config['close_button_text'] }}</button>
        </div>
    </div>
</div>


<script src="{{ asset('flipbook/js/three.min.js') }}"></script>
<script src="{{ asset('flipbook/js/pdf.min.js') }}"></script>

<script type="text/javascript">
    window.PDFJS_LOCALE = {
        pdfJsWorker: "{{ asset('flipbook/js/pdf.worker.js') }}",
        pdfJsCMapUrl: "{{ asset('flipbook/cmaps') }}"
    };


</script>
<script src="{{ asset('flipbook/js/3dflipbook.js') }}"></script>

<script type="text/javascript">
    const pdfUrl = "{{ Storage::disk("s3")->url($ebook->getPdf()) }}";
        const isMobile=screen.width <= 768;
    let styles=[
                "{{ asset('flipbook/css/font-awesome.min.css') }}",
                "{{ asset('flipbook/css/short-black-book-view.css') }}"
            ];
    if(isMobile){
        styles.push("{{ asset('flipbook/css/short-black-book-view-res.css') }}");
    }
    $('.sample-container').FlipBook({
        pdf: pdfUrl,
        controlsProps: {
            downloadURL: pdfUrl,
            actions: {
                cmdBackward: {
                    code: 37,
                },
                cmdForward: {
                    code: 39
                },
                cmdSave: {
                    code: 68,
                    flags: 1,
                }
            },
            loadingAnimation: {
                book: true
            },
            autoResolution: {
                enabled: true
            },
            scale: {
                max: 2
            }
        },
        template: {
            html: "{{ asset('flipbook/templates/default-book-view.html') }}",
            styles: styles,
            script: "{{ asset('flipbook/js/default-book-view.js') }}",
            sounds: {
                startFlip: "{{ asset('flipbook/sounds/start-flip.mp3') }}",
                endFlip: "{{ asset('flipbook/sounds/end-flip.mp3') }}"
            }
        }
    });

    // Review Modal JavaScript
    $(document).ready(function() {
        const campaignId = {{ $ebook->campaign->id }};
        const localStorageKey = `ebook_review_submitted_${campaignId}`;
        const reviewModal = document.getElementById('reviewModal');
        const closeModalBtn = document.querySelector('.close-modal');
        const ratingInput = document.getElementById('rating');
        const stars = document.querySelectorAll('.star');
        const ratingText = document.querySelector('.rating-text');
        const reviewForm = document.getElementById('reviewForm');
        const thankYouMessage = document.getElementById('thankYouMessage');
        const closeThankYouBtn = document.querySelector('.close-thank-you');

        // Function to ensure the modal is properly displayed
        function showReviewModal() {
            console.log('showReviewModal called');
            // return;
            // First, make sure the modal is visible
            reviewModal.style.display = 'flex';

            // Force the modal to be visible with high z-index and !important
            reviewModal.setAttribute('style', 'display: flex !important; z-index: 9999 !important;');

            // Make sure the modal content is visible and centered
            const modalContent = reviewModal.querySelector('.review-modal-content');
            if (modalContent) {
                modalContent.setAttribute('style', 'opacity: 1 !important; transform: scale(1) !important;');
            }

            // Prevent scrolling on the body when modal is open
            document.body.style.overflow = 'hidden';

            // Focus on the first input field
            const firstInput = reviewModal.querySelector('input[type="text"]');
            if (firstInput) {
                setTimeout(() => {
                    firstInput.focus();
                }, 100);
            }
        }

        // Initialize star rating
        function initStarRating() {
            // Set initial rating (5 stars)
            updateStars(5);

            // Add event listeners to stars
            stars.forEach(star => {
                // Handle click on star
                star.addEventListener('click', function() {
                    const rating = parseInt(this.getAttribute('data-rating'));
                    updateStars(rating);
                    ratingInput.value = rating;
                    updateRatingText(rating);
                });

                // Handle hover effects
                star.addEventListener('mouseenter', function() {
                    const rating = parseInt(this.getAttribute('data-rating'));
                    highlightStars(rating);
                });

                star.addEventListener('mouseleave', function() {
                    const currentRating = parseInt(ratingInput.value);
                    updateStars(currentRating);
                });
            });
        }

        // Update stars based on rating
        function updateStars(rating) {
            stars.forEach(star => {
                const starRating = parseInt(star.getAttribute('data-rating'));
                if (starRating <= rating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        // Temporarily highlight stars on hover
        function highlightStars(rating) {
            stars.forEach(star => {
                const starRating = parseInt(star.getAttribute('data-rating'));
                if (starRating <= rating) {
                    star.classList.add('active');
                } else {
                    star.classList.remove('active');
                }
            });
        }

        // Update the rating text based on the selected rating
        function updateRatingText(rating) {
            let text = '';
            switch(rating) {
                case 1:
                    text = 'Poor (1/5)';
                    break;
                case 2:
                    text = 'Fair (2/5)';
                    break;
                case 3:
                    text = 'Good (3/5)';
                    break;
                case 4:
                    text = 'Very Good (4/5)';
                    break;
                case 5:
                    text = 'Excellent (5/5)';
                    break;
                default:
                    text = 'Please select a rating';
            }
            ratingText.textContent = text;
        }

        // Initialize the star rating system
        initStarRating();

        // Function to hide the modal and restore body scrolling
        function hideReviewModal() {
            reviewModal.style.display = 'none';
            document.body.style.overflow = '';
        }

        // Close modal when clicking the close button
        closeModalBtn.addEventListener('click', function() {
            hideReviewModal();
        });

        // Close thank you message and modal when clicking the close button
        closeThankYouBtn.addEventListener('click', function() {
            hideReviewModal();
            reviewForm.style.display = 'block';
            thankYouMessage.style.display = 'none';
        });

        // Close modal when clicking outside the modal content
        window.addEventListener('click', function(event) {
            if (event.target === reviewModal) {
                hideReviewModal();
            }
        });

        // Handle form submission
        $('#ebookReviewForm').on('submit', function(e) {
            e.preventDefault();

            const formData = $(this).serialize();

            $.ajax({
                url: '{{ route("reviews.store") }}',
                type: 'POST',
                data: formData,
                headers: {
                    'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content')
                },
                success: function(response) {
                    // Show thank you message
                    reviewForm.style.display = 'none';
                    thankYouMessage.style.display = 'block';

                    // Set localStorage to prevent showing the modal again
                    localStorage.setItem(localStorageKey, 'true');

                    // Close modal after 3 seconds
                    setTimeout(function() {
                        hideReviewModal();
                        reviewForm.style.display = 'block';
                        thankYouMessage.style.display = 'none';
                    }, 3000);
                },
                error: function(xhr) {
                    console.error('Error submitting review:', xhr.responseText);
                    alert('There was an error submitting your review. Please try again.');
                }
            });
        });

        // Show review modal after configured seconds if not already submitted
        setTimeout(function() {
            if (!localStorage.getItem(localStorageKey)) {
                @if($ebook->user?->getUser()?->hasLeadCollection())
                    showReviewModal();
                @endif
            }
        }, {{ $ebook->campaign->getReviewFormConfig()['show_after_seconds'] * 1000 }}); // Convert seconds to milliseconds

        // Add exit intent detection using mouse movement (more reliable than beforeunload)
        let mouseY = 0;
        let exitIntentDetected = false;

        // Track mouse position
        document.addEventListener('mousemove', function(e) {
            mouseY = e.clientY;
        });

        // Detect when mouse moves toward the top of the browser
        document.addEventListener('mouseout', function(e) {
            // If the mouse is near the top of the viewport and moving out of the window
            if (mouseY < 50 && e.clientY <= 0 && e.relatedTarget === null && !localStorage.getItem(localStorageKey) && !exitIntentDetected) {
                exitIntentDetected = true;
                showReviewModal();

                // Reset the flag after 30 seconds
                setTimeout(() => {
                    exitIntentDetected = false;
                }, 30000);
            }
        });

        // Track if the user is trying to leave the page
        let isLeavingPage = false;

        // Show review modal when user tries to leave the page
        // window.addEventListener('beforeunload', function(e) {
        //     if (!localStorage.getItem(localStorageKey) && !isLeavingPage) {
        //         // Set flag to prevent multiple triggers
        //         isLeavingPage = true;

        //         // Show the review modal
        //         showReviewModal();

        //         // Set a confirmation message (may not be displayed in modern browsers)
        //         const confirmationMessage = 'Before you go, we\'d love your feedback!';
        //         e.returnValue = confirmationMessage;

        //         // Reset the flag after a short delay
        //         setTimeout(() => {
        //             isLeavingPage = false;
        //         }, 500);

        //         return confirmationMessage;
        //     }
        // });

        // Additional exit intent detection - more reliable than beforeunload
        document.addEventListener('visibilitychange', function() {
            if (document.visibilityState === 'hidden' && !localStorage.getItem(localStorageKey)) {
                // User is switching tabs or minimizing - good time to show the modal when they return
                setTimeout(() => {
                    if (document.visibilityState === 'visible' && !localStorage.getItem(localStorageKey)) {
                        showReviewModal();
                    }
                }, 1000);
            }
        });
    });
</script>

<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
