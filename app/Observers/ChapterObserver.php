<?php

namespace App\Observers;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\Chapter;

class ChapterObserver
{
    /**
     * Handle the Chapter "created" event.
     */
    public function created(Chapter $chapter): void
    {
        //
    }

    /**
     * Handle the Chapter "updated" event.
     */
    public function updated(Chapter $chapter): void
    {
        if ($this->isChapterCompleted($chapter)) {
            $campaign = $chapter->campaign;
            $campaign->log("Chapter is completed.");

            if ($this->areAllChaptersCompleted($campaign)) {
                $campaign->log("All chapters are completed. Updating campaign status");
                $this->updateCampaignStatus($campaign);
            }
        }
    }

    /**
     * Handle the Chapter "deleted" event.
     */
    public function deleted(Chapter $chapter): void
    {
        //
    }

    /**
     * Handle the Chapter "restored" event.
     */
    public function restored(Chapter $chapter): void
    {
        //
    }

    /**
     * Handle the Chapter "force deleted" event.
     */
    public function forceDeleted(Chapter $chapter): void
    {
        //
    }

    private function isChapterCompleted(Chapter $chapter): bool
    {
        return $chapter->status === CampaignStatusEnum::DONE;
    }

    private function areAllChaptersCompleted(Campaign $campaign): bool
    {
        return $campaign->chapters()
                        ->where('status', '!=', CampaignStatusEnum::DONE)
                        ->doesntExist();
    }

    private function updateCampaignStatus(Campaign $campaign): void
    {
        $newStatus = $campaign->image()
            ? CampaignStatusEnum::IMAGE_PROCESSING
            : CampaignStatusEnum::DONE;

        $campaign->update(['status' => $newStatus]);

        //session()->forget("campaign_ebook_plan_" . $campaign->id);
    }
}
