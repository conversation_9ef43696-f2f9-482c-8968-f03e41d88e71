<?php

namespace App\Observers;

use App\Actions\RunCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Enum\CreditLogActionEnum;
use App\Jobs\DispatchChapterSectionsJob;
use App\Jobs\GenerateCoverImage;
use App\Jobs\GenerateEbookChaptersJob;
use App\Jobs\GenerateEbookJob;
use App\Jobs\GenerateEbookTitleJob;
use App\Jobs\ImageProcessingJob;
use App\Models\Campaign;
use App\Service\Contracts\CampaignImageProcessingServiceInterface;
use App\Traits\HasCreditLog;
use Illuminate\Bus\Batch;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Log;

class CampaignObserver
{
    use HasCreditLog;
    protected RunCampaignAction $runCampaignAction;
    protected CampaignImageProcessingServiceInterface $imageProcessingService;
    public function __construct(
        RunCampaignAction $runCampaignAction,
        CampaignImageProcessingServiceInterface $imageProcessingService
    )
    {
        $this->runCampaignAction = $runCampaignAction;
        $this->imageProcessingService = $imageProcessingService;
    }
    /**
     * Handle the Campaign "created" event.
     */
    public function created(Campaign $campaign): void
    {
        if ($campaign->status->value == CampaignStatusEnum::IN_PROGRESS->value ||
            $campaign->status->value == CampaignStatusEnum::PENDING->value){
            $campaign->log("Starting ebook campaign");
            $this->runCampaignAction->execute($campaign);
        }
    }

    /**
     * Handle the Campaign "updated" event.
     */
    public function updated(Campaign $campaign): void
    {
        if ($campaign->wasChanged('status') && $campaign->status == CampaignStatusEnum::IMAGE_PROCESSING) {
            $campaign->log("Image processing for ebook campaign starting.");
            $this->imageProcessingService->processImagesForCampaign($campaign);
        }

        if($campaign->status == CampaignStatusEnum::DONE && (
            $campaign->getOriginal('status') == CampaignStatusEnum::IN_PROGRESS ||
           $campaign->getOriginal('status') == CampaignStatusEnum::IMAGE_PROCESSING)
        ){
            $campaign->log("Starting ebook generation from observer");

            if($campaign->getForm("generate_ai_cover_image")){
                GenerateCoverImage::dispatch($campaign->id);
            }
            GenerateEbookJob::dispatch($campaign->id);
        }
    }

    /**
     * Handle the Campaign "deleted" event.
     */
    public function deleted(Campaign $campaign): void
    {
        //
    }

    /**
     * Handle the Campaign "restored" event.
     */
    public function restored(Campaign $campaign): void
    {
        //
    }

    /**
     * Handle the Campaign "force deleted" event.
     */
    public function forceDeleted(Campaign $campaign): void
    {
        //
    }
}
