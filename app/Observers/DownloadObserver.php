<?php

namespace App\Observers;

use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Enum\CreditLogActionEnum;
use App\Models\Download;

class DownloadObserver
{
    /**
     * Handle the Download "created" event.
     */
    public function created(Download $download): void
    {
        $campaign = $download->campaign;
        $user = $download->user;

        $campaign->log("Deducting credit start");

        $downloadTypes = $campaign->downloads()
                                  ->whereIn('type', ['pdf', 'epub']) // Replace with Enum if available
                                  ->select('type')
                                  ->distinct()
                                  ->pluck('type')
                                  ->all();

        $hasPdfAndEpub = in_array('pdf', $downloadTypes) && in_array('epub', $downloadTypes);

        $campaign->log("{$hasPdfAndEpub}, {$campaign->charged}"
        );

        // When NOT charged and both types exist
        if (
            !$campaign->charged &&
            $hasPdfAndEpub
        ) {
            $campaign->log("Campaign is done and charged is false. Updating credit use and charged to true.");

            $campaign->update(['charged' => true]);

            $requiredCredits = $campaign->requiredCredits();

            if ($user) {
                $user->logCreditActivity(
                    user: $user,
                    action: CreditLogActionEnum::CAMPAIGN,
                    credit: $requiredCredits,
                    description: "{$requiredCredits} Credit deduct for ebook campaign: {$campaign->id}",
                );

                $user->useCredit($requiredCredits);
            }
        }
        // If already charged, only deduct ebook generation credit
        elseif ($campaign->charged && $user && in_array($download->type, ['pdf', 'epub'])) {
            $credit = CreditDeductEnum::EBOOK_GENERATION_CREDIT->value / 2;

            $user->logCreditActivity(
                user: $user,
                action: CreditLogActionEnum::CAMPAIGN,
                credit: $credit,
                description: "{$credit} Credit deduct for {$download->type} ebook regeneration (campaign already charged): {$campaign->id}",
            );

            $user->useCredit($credit);
        }
        elseif ($campaign->charged && $user && in_array($download->type, ['mp3'])) {
            $credit = CreditDeductEnum::AUDIO_GENERATION_CREDIT->value;

            $user->logCreditActivity(
                user: $user,
                action: CreditLogActionEnum::CAMPAIGN,
                credit: $credit,
                description: "{$credit} Credit deduct for audio generation (campaign already charged): {$campaign->id}",
            );

            $user->useCredit($credit);
        }
    }



    /**
     * Handle the Download "updated" event.
     */
    public function updated(Download $download): void
    {
        //
    }

    /**
     * Handle the Download "deleted" event.
     */
    public function deleted(Download $download): void
    {
        //
    }

    /**
     * Handle the Download "restored" event.
     */
    public function restored(Download $download): void
    {
        //
    }

    /**
     * Handle the Download "force deleted" event.
     */
    public function forceDeleted(Download $download): void
    {
        //
    }
}
