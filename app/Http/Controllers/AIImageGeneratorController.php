<?php
namespace App\Http\Controllers;
use App\Service\AIModel\OpenAIClient;
use App\Service\FinalizeImageService;
use Illuminate\Http\Request;

class AIImageGeneratorController extends Controller
{
    public function generateImage(Request $request)
    {
        $openAIClient = new OpenAIClient();
        $text = $request->input('text');
        // $optimizedImage = new FinalizeImageService();
        $imageUrl = $openAIClient->generateImage($text, auth()->user()->getOpenAiApiKey());
        // $optimizeImageUrl = $optimizedImage->handleImage($imageUrl, strip_tags($text));
        return response()->json([
            'url' => $imageUrl
        ]);

    }
}
