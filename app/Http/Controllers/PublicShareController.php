<?php

namespace App\Http\Controllers;


use App\Models\Download;

use App\Traits\EncryptHelper;

class PublicShareController extends Controller
{
    use EncryptHelper;
    public function __invoke(string $hash)
    {
        $recordId = EncryptHelper::decrypt($hash);
        $ebook = Download::findOrFail($recordId);


        abort_unless($ebook->getMeta('allow_public_access'), 404, "Content not found");
        abort_unless($ebook->getMeta('public_share_hash') === $hash, 404, "Content not found");
        
        if (!isS3FileExist($ebook->getPdf())) {
            abort(404);
        }

        return view('ebook.ebook-flipbook', compact('ebook'));
    }
}
