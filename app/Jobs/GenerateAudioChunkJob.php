<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Mail\AudioBookMail;
use App\Models\Campaign;
use App\Models\Download;
use App\Service\AIModel\Contracts\AIClientInterface;
use App\Service\AIModel\OpenAIClient;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Str;
use Throwable;

class GenerateAudioChunkJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;


    protected $campaign;
    protected $textChunk;
    protected $chunkIndex;

    public function __construct(Campaign $campaign, $textChunk, $chunkIndex)
    {
        $this->campaign = $campaign;
        // Clean and ensure proper UTF-8 encoding before serialization
        $this->textChunk = $this->cleanUtf8Text($textChunk);
        $this->chunkIndex = $chunkIndex;
    }

    /**
     * Clean and ensure proper UTF-8 encoding
     */
    private function cleanUtf8Text($text)
    {
        // Remove any null bytes and control characters
        $text = str_replace("\0", '', $text);

        // Remove or replace problematic characters
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/', '', $text);

        // Convert to UTF-8 and remove invalid sequences
        $text = mb_convert_encoding($text, 'UTF-8', 'UTF-8');

        // Remove any remaining invalid UTF-8 sequences using regex
        $text = preg_replace('/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F\xFF]/', '', $text);

        // Ensure it's valid UTF-8
        if (!mb_check_encoding($text, 'UTF-8')) {
            // If still invalid, convert from ISO-8859-1 to UTF-8
            $text = mb_convert_encoding($text, 'UTF-8', 'ISO-8859-1');
        }

        return trim($text);
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        try {
            $this->campaign->log("Started generating audio chunk " . $this->chunkIndex);
            $this->campaign->log("Text chunk " . $this->textChunk);

            if ($this->campaign->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED) {
                return;
            }
            $openAIClient = new OpenAIClient();
            $this->campaign->log("Generating audio chunk " . $this->chunkIndex . " (attempt {$this->attempts()}/{$this->tries})");

            // Add retry logic specifically for API calls
            $maxApiRetries = 3;
            $apiRetryDelay = 5; // seconds
            $audioData = null;

            for ($apiAttempt = 1; $apiAttempt <= $maxApiRetries; $apiAttempt++) {
                try {
                    // Text is already cleaned and UTF-8 encoded in constructor
                    $audioData = $openAIClient->generateAudio($this->textChunk, $this->campaign->getOpenAiApiKey(), $this->campaign->getMeta("voice_model"));
                    break; // Success, exit retry loop
                } catch (\Exception $apiException) {
                    $this->campaign->log("API attempt {$apiAttempt}/{$maxApiRetries} failed for chunk {$this->chunkIndex}: " . $apiException->getMessage());

                    if ($apiAttempt < $maxApiRetries) {
                        $this->campaign->log("Retrying API call in {$apiRetryDelay} seconds...");
                        sleep($apiRetryDelay);
                        $apiRetryDelay *= 2; // Exponential backoff
                    } else {
                        throw $apiException; // Re-throw after all API retries exhausted
                    }
                }
            }

            if (empty($audioData)) {
                throw new \Exception("Generated audio data is empty for chunk " . $this->chunkIndex);
            }

            $fileName = "chunk-" . $this->campaign->id . "-" . $this->chunkIndex . ".mp3";
            Storage::put('public/audio/' . $fileName, $audioData);
            $audioChunks = $this->campaign->getMeta('chunks', []);
            array_push($audioChunks, [
                "chunk_index" => $this->chunkIndex,
                'file_name' => $fileName
            ]);
            try{
                $this->campaign->saveMeta('chunks', $audioChunks);
            }
            catch (\Exception $e){
                $this->campaign->log("Failed to save audio chunk metadata. " . formatLogMessage($e->getMessage()));
            }
            $this->campaign->log("Generated " .count($audioChunks)." of ".$this->campaign->getMeta("total_chunk"));
            //check if all chunks completed
            if (count($audioChunks) == $this->campaign->getMeta("total_chunk")) {
                $this->campaign->log("Merging audio chunks");
                $mergedAudioData = '';
                $missingChunks = [];

                foreach ($audioChunks as $chunk) {
                    $filePath = storage_path("app/public/audio/{$chunk['file_name']}");
                    if (file_exists($filePath)) {
                        $chunkData = file_get_contents($filePath);
                        if ($chunkData !== false) {
                            $mergedAudioData .= $chunkData;
                        } else {
                            $missingChunks[] = $chunk['file_name'];
                            $this->campaign->log("Failed to read audio chunk: " . $filePath);
                        }
                    } else {
                        $missingChunks[] = $chunk['file_name'];
                        $this->campaign->log("Missing audio chunk: " . $filePath);
                    }
                }

                if (!empty($missingChunks)) {
                    throw new \Exception("Missing or unreadable audio chunks: " . implode(', ', $missingChunks));
                }

                if (empty($mergedAudioData)) {
                    throw new \Exception("Merged audio data is empty");
                }
                $audioFileName = Str::slug($this->campaign->topic) . '.mp3';
                $this->campaign->log("Generated audio filename " . $audioFileName);

                $uploadSuccess = Storage::disk('s3')->put('audio/' . $audioFileName, $mergedAudioData);

                if (!$uploadSuccess) {
                    throw new \Exception("Failed to upload merged audio file to S3");
                }

                $this->campaign->saveMeta('audio', $audioFileName);
                
                Download::create([
                    'type' => 'mp3',
                    "path" => 'audio/'.$audioFileName,
                    "cover_image" => null,
                    'file_version' => 1,
                    "user_id" => $this->campaign->user_id,
                    "campaign_id" => $this->campaign->id,
                ]);
                
                //delete all the chunks
                foreach ($audioChunks as $chunk) {
                    try {
                        Storage::delete('public/audio/' . $chunk['file_name']);
                    } catch (\Exception $e) {
                        $this->campaign->log("Failed to delete chunk file: " . $chunk['file_name'] . " - " . $e->getMessage());
                    }
                }

                // Clear chunks metadata
                // $this->campaign->saveMeta('voice_model', null);
                $this->campaign->saveMeta('chunks', []);

                //status to done
                $this->campaign->update([
                    'status' => CampaignStatusEnum::DONE,
                ]);
                if (!Storage::disk('s3')->exists('audio/' . $audioFileName)) {
                    $this->campaign->log("Failed to upload audio file to S3");
                    $this->campaign->update([
                        'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
                    ]);
                    return;
                }
                Mail::to($this->campaign->user->email)->send(new AudioBookMail($this->campaign));

            }


        } catch (\Exception $e) {
            $this->campaign->log("Failed to generate audio chunk {$this->chunkIndex}. Attempt {$this->attempts()}/{$this->tries}. Error: " . formatLogMessage($e->getMessage()));

            // Only mark as failed if this is the final attempt
            if ($this->attempts() >= $this->tries) {
                $this->campaign->log("All retry attempts exhausted for chunk {$this->chunkIndex}. Marking campaign as failed.");
                $this->campaign->update([
                    'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
                ]);
            } else {
                $this->campaign->log("Will retry chunk {$this->chunkIndex} in {$this->backoff} seconds.");
                // Re-throw the exception to trigger retry
                throw $e;
            }
        }
    }
    public function failed(?Throwable $exception)
    {
        $this->campaign->log("FINAL FAILURE: Audio chunk {$this->chunkIndex} failed after {$this->tries} attempts. Error: " . formatLogMessage($exception->getMessage()));
        $this->campaign->update([
            'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
        ]);
    }

}
