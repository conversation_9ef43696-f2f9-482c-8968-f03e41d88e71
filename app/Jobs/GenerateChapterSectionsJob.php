<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Models\Section;
use App\Service\GenerateChapterSectionService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class GenerateChapterSectionsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds

    public $tries = 3;


    protected $chapter;

    /**
     * Create a new job instance.
     */
    public function __construct($chapter = null)
    {
        $this->chapter = $chapter;
    }

    /**
     * Execute the job.
     */
    public function handle(GenerateChapterSectionService $sectionService): void
    {
        $campaign = $this->chapter->campaign;

        if ($campaign->status == CampaignStatusEnum::FAILED){
            $campaign->log("Campaign is failed.");
            return;
        }

        $campaign->log("Generating chapter's sections");
        $sectionService->execute($campaign, $this->chapter);
    }

    public function failed(?Throwable $exception)
    {
        $this->chapter->campaign->log("Failed generating chapter's sections. " . formatLogMessage($exception->getMessage()));
        $this->chapter->campaign->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $this->chapter->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $this->chapter->campaign->addErrorToMeta('chapter_section_generate_error', "Chapter's section generation failed " . $exception->getMessage());

        throw new \Exception("Chapter's section generation failed " . $exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
