<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Bus;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class DispatchChapterSectionsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds


    protected Campaign $campaign;

    /**
     * Create a new job instance.
     */
    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if ($this->campaign->status == CampaignStatusEnum::FAILED){
            $this->campaign->log("Campaign is failed.");
            return;
        }

        $chapters = $this->campaign->chapters;
        if ($chapters->count() === 0) {
            return;
        }

        $this->campaign->log("Campaign chapters for dispatch jobs: " . json_encode($chapters));

        $jobsDispatched = false;

        foreach ($chapters as $chapter) {
            if ($chapter->sections->count() == 0) {
                GenerateChapterSectionsJob::dispatch($chapter)->onQueue($chapter->getChapterQueueName());
                $jobsDispatched = true;
            }
        }

        if ($jobsDispatched) {
            $this->campaign->log("Jobs for generating chapter sections have been dispatched.");
        } else {
            $this->campaign->log("No jobs for generating chapter sections, so marking the campaign as done.");
            $this->campaign->status = CampaignStatusEnum::DONE;
            $this->campaign->save();

            // session()->forget("campaign_ebook_plan_" . $this->campaign->id);
        }
    }

    public function failed(?Throwable $exception)
    {
        $this->campaign->log("Failed to generate Chapter's sections for dispatch jobs: " . formatLogMessage($exception->getMessage()));
        $this->campaign->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $this->campaign->addErrorToMeta('chapter_section_failed', "Failed to generate Chapter's sections " . formatLogMessage($exception->getMessage()));

        throw new \Exception("Job title generate failed! " . $exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
