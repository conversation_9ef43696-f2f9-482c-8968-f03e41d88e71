<?php

namespace App\Jobs;

use App\Enum\AICoverImageRatioEnum;
use App\Enum\AIModelEnum;
use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class GenerateCoverImage implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds


    protected int $ebookId;

    public function __construct(int $ebookId)
    {
        $this->ebookId = $ebookId;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        $campaign = Campaign::find($this->ebookId);
//        if ($campaign->cover_image){
//            return;
//        }
        $title = $campaign->getForm('cover_image_title') ?? $campaign->title;
        try {
            $aiClient = app(AIClientInterface::class, ['aiModel' => AIModelEnum::OPEN_AI->value]);
            $data = [
                'title' => strip_tags($title),
                'page_size' => optional($campaign->ebookFormat)->page_size ?? "A4",
            ];

            $campaign->log("Ebook cover image prompt data" . json_encode($data));

            $prompt = promptBuilder(prompts()['cover_image'], $data);

            $campaign->log("Ebook cover image prompt" . formatLogMessage($prompt));

            $size = optional($campaign->ebookFormat)->page_size ?? "A4"; //it will return a4, letter, legal
            $sizeDimension = AICoverImageRatioEnum::fromString($size);

            $coverImage = $aiClient->generateCoverImage($prompt, $campaign->getOpenAiApiKey(), $sizeDimension->getSize());

            $campaign->log("Generated ebook cover image: " . formatLogMessage($coverImage));

            $campaign->update([
                "cover_image" => $coverImage,
            ]);
            $campaign->log("Ebook cover image: " . formatLogMessage($coverImage));

        } catch (\Exception $e){
            $campaign->log("Failed to generate ebook cover image from AI model. " . formatLogMessage($e->getMessage()));

            $campaign->update([
                "status" => CampaignStatusEnum::FAILED,
            ]);

            $campaign->addErrorToMeta("failed_cover_image", "Failed to generate ebook cover image from AI model. " . formatLogMessage($e->getMessage()));
            throw new  \Exception("Failed to generate ebook cover image from AI model.");
        }
    }
}
