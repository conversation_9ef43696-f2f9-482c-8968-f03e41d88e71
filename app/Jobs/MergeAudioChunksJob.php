<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Mail\AudioBookMail;
use App\Models\Campaign;
use App\Models\Download;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Process;
use Illuminate\Support\Str;
use Carbon\Carbon;
use Throwable;

class MergeAudioChunksJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (2 hours).
     *
     * @var int
     */
    public $timeout = 7200; // 2 hours in seconds

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    protected $campaign;

    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->campaign->log("Starting audio merge process (attempt {$this->attempts()}/{$this->tries})");

            // Use transaction to prevent multiple merge operations
            $shouldMerge = DB::transaction(function () {
                $this->campaign->refresh();

                // Check if already processed (campaign status check)
                if ($this->campaign->status == CampaignStatusEnum::DONE) {
                    $this->campaign->log("Campaign already completed, skipping merge");
                    return false;
                }

                if ($this->campaign->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED) {
                    $this->campaign->log("Campaign already failed, skipping merge");
                    return false;
                }

                // Check for stale merge flag (older than 30 minutes)
                $mergingStartTime = $this->campaign->getMeta('merging_started_at', null);
                $processingFlag = $this->campaign->getMeta('merging_in_progress', false);

                if ($processingFlag && $mergingStartTime) {
                    $startTime = \Carbon\Carbon::parse($mergingStartTime);
                    $minutesElapsed = $startTime->diffInMinutes(now());

                    if ($minutesElapsed > 30) {
                        $this->campaign->log("Stale merge flag detected (started {$minutesElapsed} minutes ago), clearing and proceeding");
                        $this->campaign->saveMeta('merging_in_progress', false);
                        $this->campaign->saveMeta('merging_started_at', null);
                        $processingFlag = false;
                    }
                }

                if ($processingFlag) {
                    $this->campaign->log("Another merge job is already running, skipping");
                    return false;
                }

                // Set the flag with timestamp
                $this->campaign->saveMeta('merging_in_progress', true);
                $this->campaign->saveMeta('merging_started_at', now()->toISOString());
                return true;
            });

            if (!$shouldMerge) {
                return; // Exit early if another job is handling the merge
            }

            $this->campaign->log("Merging audio chunks");
            
            // Get all chunks
            $audioChunks = $this->campaign->getMeta('chunks', []);
            $totalChunks = $this->campaign->getMeta("total_chunk");
            
            // Validate we have all chunks
            if (count($audioChunks) < $totalChunks) {
                throw new \Exception("Not all chunks are ready. Have " . count($audioChunks) . " of " . $totalChunks);
            }

            // Sort chunks by index to ensure proper order
            usort($audioChunks, function($a, $b) {
                return $a['chunk_index'] <=> $b['chunk_index'];
            });

            // Use FFmpeg for memory-efficient audio merging
            $mergedAudioData = $this->mergeAudioChunksWithFFmpeg($audioChunks);

            if (empty($mergedAudioData)) {
                throw new \Exception("Merged audio data is empty after FFmpeg processing");
            }

            $audioFileName = Str::slug($this->campaign->topic) . '.mp3';
            $this->campaign->log("Generated audio filename " . $audioFileName);

            $uploadSuccess = Storage::disk('s3')->put('audio/' . $audioFileName, $mergedAudioData);

            if (!$uploadSuccess) {
                throw new \Exception("Failed to upload merged audio file to S3");
            }

            $this->campaign->saveMeta('audio', $audioFileName);
            
            Download::create([
                'type' => 'mp3',
                "path" => 'audio/'.$audioFileName,
                "cover_image" => null,
                'file_version' => 1,
                "user_id" => $this->campaign->user_id,
                "campaign_id" => $this->campaign->id,
            ]);
            
            //delete all the chunks
            foreach ($audioChunks as $chunk) {
                try {
                    Storage::delete('public/audio/' . $chunk['file_name']);
                } catch (\Exception $e) {
                    $this->campaign->log("Failed to delete chunk file: " . $chunk['file_name'] . " - " . $e->getMessage());
                }
            }

            // Clear chunks metadata and merging flag
            $this->campaign->saveMeta('chunks', []);
            $this->campaign->saveMeta('merging_in_progress', false);
            $this->campaign->saveMeta('merging_started_at', null);

            //status to done
            $this->campaign->update([
                'status' => CampaignStatusEnum::DONE,
            ]);

            // Verify upload was successful
            if (!Storage::disk('s3')->exists('audio/' . $audioFileName)) {
                $this->campaign->log("Failed to upload audio file to S3");
                $this->campaign->update([
                    'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
                ]);
                return;
            }

            $this->campaign->log("Audio merge completed successfully");
            Mail::to($this->campaign->user->email)->send(new AudioBookMail($this->campaign));

        } catch (\Exception $e) {
            $this->campaign->log("Failed to merge audio. Attempt {$this->attempts()}/{$this->tries}. Error: " . formatLogMessage($e->getMessage()));
            
            // Clean up merging flag
            try {
                $this->campaign->saveMeta('merging_in_progress', false);
                $this->campaign->saveMeta('merging_started_at', null);
            } catch (\Exception) {
                // Ignore cleanup errors
            }
            
            // Only mark as failed if this is the final attempt
            if ($this->attempts() >= $this->tries) {
                $this->campaign->log("All merge retry attempts exhausted. Marking campaign as failed.");
                $this->campaign->update([
                    'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
                ]);
            } else {
                $this->campaign->log("Will retry merge in {$this->backoff} seconds.");
                // Re-throw the exception to trigger retry
                throw $e;
            }
        }
    }

    public function failed(?Throwable $exception)
    {
        $this->campaign->log("FINAL FAILURE: Audio merge failed after {$this->tries} attempts. Error: " . formatLogMessage($exception->getMessage()));
        
        // Clean up merging flag
        try {
            $this->campaign->saveMeta('merging_in_progress', false);
            $this->campaign->saveMeta('merging_started_at', null);
        } catch (\Exception) {
            // Ignore cleanup errors
        }
        
        $this->campaign->update([
            'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
        ]);
    }

    /**
     * Merge audio chunks using FFmpeg for memory efficiency
     *
     * @param array $audioChunks
     * @return string The merged audio file content
     * @throws \Exception
     */
    private function mergeAudioChunksWithFFmpeg(array $audioChunks): string
    {
        $tempDir = sys_get_temp_dir() . '/audio_merge_' . $this->campaign->id . '_' . time() . '_' . uniqid();
        $concatListFile = $tempDir . '/concat_list.txt';
        $outputFile = $tempDir . '/merged_audio.mp3';

        try {
            // Create temporary directory (clean up if it exists)
            if (is_dir($tempDir)) {
                array_map('unlink', glob($tempDir . '/*'));
                rmdir($tempDir);
            }
            if (!mkdir($tempDir, 0755, true)) {
                throw new \Exception("Failed to create temporary directory: $tempDir");
            }

            $this->campaign->log("Created temporary directory: $tempDir");

            // Validate and prepare chunk files
            $validChunks = [];
            $missingChunks = [];

            foreach ($audioChunks as $chunk) {
                $sourcePath = storage_path("app/public/audio/{$chunk['file_name']}");

                if (!file_exists($sourcePath)) {
                    $missingChunks[] = $chunk['file_name'];
                    $this->campaign->log("Missing audio chunk: " . $sourcePath);
                    continue;
                }

                // Verify file is readable and not empty
                if (!is_readable($sourcePath) || filesize($sourcePath) === 0) {
                    $missingChunks[] = $chunk['file_name'];
                    $this->campaign->log("Unreadable or empty audio chunk: " . $sourcePath);
                    continue;
                }

                $validChunks[] = [
                    'source_path' => $sourcePath,
                    'chunk_index' => $chunk['chunk_index'],
                    'file_name' => $chunk['file_name']
                ];

                $this->campaign->log("Validated chunk {$chunk['chunk_index']}: {$chunk['file_name']}");
            }

            if (!empty($missingChunks)) {
                throw new \Exception("Missing or unreadable audio chunks: " . implode(', ', $missingChunks));
            }

            if (empty($validChunks)) {
                throw new \Exception("No valid audio chunks found for merging");
            }

            // Create FFmpeg concat list file
            $concatContent = '';
            foreach ($validChunks as $chunk) {
                // Escape file path for FFmpeg
                $escapedPath = str_replace("'", "'\\''", $chunk['source_path']);
                $concatContent .= "file '$escapedPath'\n";
            }

            if (file_put_contents($concatListFile, $concatContent) === false) {
                throw new \Exception("Failed to create FFmpeg concat list file");
            }

            $this->campaign->log("Created FFmpeg concat list with " . count($validChunks) . " chunks");

            // Execute FFmpeg command
            $ffmpegCommand = [
                'ffmpeg',
                '-f', 'concat',
                '-safe', '0',
                '-i', $concatListFile,
                '-c', 'copy',
                '-y', // Overwrite output file
                $outputFile
            ];

            $this->campaign->log("Executing FFmpeg command: " . implode(' ', $ffmpegCommand));

            $result = Process::run($ffmpegCommand);

            if (!$result->successful()) {
                throw new \Exception("FFmpeg failed with exit code {$result->exitCode()}: " . $result->errorOutput());
            }

            $this->campaign->log("FFmpeg merge completed successfully");

            // Verify output file exists and is not empty
            if (!file_exists($outputFile) || filesize($outputFile) === 0) {
                throw new \Exception("FFmpeg output file is missing or empty");
            }

            // Read the merged audio file
            $mergedAudioData = file_get_contents($outputFile);
            if ($mergedAudioData === false) {
                throw new \Exception("Failed to read merged audio file");
            }

            $this->campaign->log("Successfully read merged audio file (" . strlen($mergedAudioData) . " bytes)");

            return $mergedAudioData;

        } finally {
            // Clean up temporary files
            $this->cleanupTempDirectory($tempDir);
        }
    }

    /**
     * Clean up temporary directory and files
     *
     * @param string $tempDir
     */
    private function cleanupTempDirectory(string $tempDir): void
    {
        try {
            if (is_dir($tempDir)) {
                $files = glob($tempDir . '/*');
                foreach ($files as $file) {
                    if (is_file($file)) {
                        unlink($file);
                    }
                }
                rmdir($tempDir);
                $this->campaign->log("Cleaned up temporary directory: $tempDir");
            }
        } catch (\Exception $e) {
            $this->campaign->log("Warning: Failed to cleanup temporary directory $tempDir: " . $e->getMessage());
        }
    }
}
