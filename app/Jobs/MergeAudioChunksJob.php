<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Mail\AudioBookMail;
use App\Models\Campaign;
use App\Models\Download;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Throwable;

class MergeAudioChunksJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (2 hours).
     *
     * @var int
     */
    public $timeout = 7200; // 2 hours in seconds

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3;

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int
     */
    public $backoff = 60;

    protected $campaign;

    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            $this->campaign->log("Starting audio merge process (attempt {$this->attempts()}/{$this->tries})");

            // Use transaction to prevent multiple merge operations
            $shouldMerge = DB::transaction(function () {
                $this->campaign->refresh();
                
                // Check if already processed (campaign status check)
                if ($this->campaign->status == CampaignStatusEnum::DONE) {
                    $this->campaign->log("Campaign already completed, skipping merge");
                    return false;
                }

                if ($this->campaign->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED) {
                    $this->campaign->log("Campaign already failed, skipping merge");
                    return false;
                }
                
                // Set a processing flag to prevent other merge jobs from running
                $processingFlag = $this->campaign->getMeta('merging_in_progress', false);
                if ($processingFlag) {
                    $this->campaign->log("Another merge job is already running, skipping");
                    return false;
                }
                
                // Set the flag
                $this->campaign->saveMeta('merging_in_progress', true);
                return true;
            });

            if (!$shouldMerge) {
                return; // Exit early if another job is handling the merge
            }

            $this->campaign->log("Merging audio chunks");
            
            // Get all chunks
            $audioChunks = $this->campaign->getMeta('chunks', []);
            $totalChunks = $this->campaign->getMeta("total_chunk");
            
            // Validate we have all chunks
            if (count($audioChunks) < $totalChunks) {
                throw new \Exception("Not all chunks are ready. Have " . count($audioChunks) . " of " . $totalChunks);
            }

            // Sort chunks by index to ensure proper order
            usort($audioChunks, function($a, $b) {
                return $a['chunk_index'] <=> $b['chunk_index'];
            });

            $mergedAudioData = '';
            $missingChunks = [];

            foreach ($audioChunks as $chunk) {
                $filePath = storage_path("app/public/audio/{$chunk['file_name']}");
                if (file_exists($filePath)) {
                    $chunkData = file_get_contents($filePath);
                    if ($chunkData !== false) {
                        $mergedAudioData .= $chunkData;
                    } else {
                        $missingChunks[] = $chunk['file_name'];
                        $this->campaign->log("Failed to read audio chunk: " . $filePath);
                    }
                } else {
                    $missingChunks[] = $chunk['file_name'];
                    $this->campaign->log("Missing audio chunk: " . $filePath);
                }
            }

            if (!empty($missingChunks)) {
                throw new \Exception("Missing or unreadable audio chunks: " . implode(', ', $missingChunks));
            }

            if (empty($mergedAudioData)) {
                throw new \Exception("Merged audio data is empty");
            }

            $audioFileName = Str::slug($this->campaign->topic) . '.mp3';
            $this->campaign->log("Generated audio filename " . $audioFileName);

            $uploadSuccess = Storage::disk('s3')->put('audio/' . $audioFileName, $mergedAudioData);

            if (!$uploadSuccess) {
                throw new \Exception("Failed to upload merged audio file to S3");
            }

            $this->campaign->saveMeta('audio', $audioFileName);
            
            Download::create([
                'type' => 'mp3',
                "path" => 'audio/'.$audioFileName,
                "cover_image" => null,
                'file_version' => 1,
                "user_id" => $this->campaign->user_id,
                "campaign_id" => $this->campaign->id,
            ]);
            
            //delete all the chunks
            foreach ($audioChunks as $chunk) {
                try {
                    Storage::delete('public/audio/' . $chunk['file_name']);
                } catch (\Exception $e) {
                    $this->campaign->log("Failed to delete chunk file: " . $chunk['file_name'] . " - " . $e->getMessage());
                }
            }

            // Clear chunks metadata and merging flag
            $this->campaign->saveMeta('chunks', []);
            $this->campaign->saveMeta('merging_in_progress', false);

            //status to done
            $this->campaign->update([
                'status' => CampaignStatusEnum::DONE,
            ]);

            // Verify upload was successful
            if (!Storage::disk('s3')->exists('audio/' . $audioFileName)) {
                $this->campaign->log("Failed to upload audio file to S3");
                $this->campaign->update([
                    'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
                ]);
                return;
            }

            $this->campaign->log("Audio merge completed successfully");
            Mail::to($this->campaign->user->email)->send(new AudioBookMail($this->campaign));

        } catch (\Exception $e) {
            $this->campaign->log("Failed to merge audio. Attempt {$this->attempts()}/{$this->tries}. Error: " . formatLogMessage($e->getMessage()));
            
            // Clean up merging flag
            try {
                $this->campaign->saveMeta('merging_in_progress', false);
            } catch (\Exception) {
                // Ignore cleanup errors
            }
            
            // Only mark as failed if this is the final attempt
            if ($this->attempts() >= $this->tries) {
                $this->campaign->log("All merge retry attempts exhausted. Marking campaign as failed.");
                $this->campaign->update([
                    'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
                ]);
            } else {
                $this->campaign->log("Will retry merge in {$this->backoff} seconds.");
                // Re-throw the exception to trigger retry
                throw $e;
            }
        }
    }

    public function failed(?Throwable $exception)
    {
        $this->campaign->log("FINAL FAILURE: Audio merge failed after {$this->tries} attempts. Error: " . formatLogMessage($exception->getMessage()));
        
        // Clean up merging flag
        try {
            $this->campaign->saveMeta('merging_in_progress', false);
        } catch (\Exception) {
            // Ignore cleanup errors
        }
        
        $this->campaign->update([
            'status' => CampaignStatusEnum::AUDIO_PROCESSING_FAILED,
        ]);
    }
}
