<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Models\Chapter;
use App\Models\Section;
use App\Service\GenerateSectionContentService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class GenerateSectionKeyPointContentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds


    protected int $chapter;
    protected int $section;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3; // Maximum 3 attempts

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int|array
     */
    public $backoff = [30, 60, 90];

    /**
     * Create a new job instance.
     */
    public function __construct(int $chapter, int $section)
    {
        $this->chapter = $chapter;
        $this->section = $section;
    }

    /**
     * Execute the job.
     */
    public function handle(GenerateSectionContentService $content): void
    {
        $chapter = Chapter::find($this->chapter);

        if ($chapter->campaign->status == CampaignStatusEnum::FAILED){
            $chapter->campaign->log("Campaign is failed.");
            return;
        }

        $section = Section::find($this->section);

        $chapter->campaign->log("Generating sections content for section {$this->section}");
        $content->execute($this->chapter, $this->section);
    }

    public function failed(?Throwable $exception)
    {
        $chapter = Chapter::find($this->chapter);
        $section = Section::find($this->section);

        $chapter->campaign->log("Failed to generate sections content for section {$this->section->id}. Error: " . formatLogMessage($exception->getMessage()));
        $chapter->campaign->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $chapter->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $section->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $chapter->campaign->addErrorToMeta('section_content_failed', "Failed to generate sections content for section {$this->section->id}. Error: " . formatLogMessage($exception->getMessage()));

        throw new \Exception("Section content generation failed " . $exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
