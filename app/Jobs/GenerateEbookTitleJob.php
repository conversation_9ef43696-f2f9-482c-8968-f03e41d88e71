<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Service\GenerateEbookTitleService;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class GenerateEbookTitleJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds

    public $tries = 3;


    protected Campaign $campaign;

    /**
     * Create a new job instance.
     */
    public function __construct(Campaign $campaign)
    {
        $this->campaign = $campaign;
    }

    /**
     * Execute the job.
     */
    public function handle(GenerateEbookTitleService $generateTitleService): void
    {
        if (!empty($this->campaign->title)) {
            $this->campaign->log("Title already exists. Skipping title generation.");
            return;
        }

        if ($this->campaign->status == CampaignStatusEnum::FAILED){
            $this->campaign->log("Campaign is failed. Skipping title generation.");
            return;
        }

        $this->campaign->log("Generating ebook title");
        $generateTitleService->execute($this->campaign);

    }

    public function failed(?Throwable $exception)
    {
        $this->campaign->log("Failed generating Ebook Title. " . formatLogMessage($exception->getMessage()));
        $this->campaign->update([
            'status' => CampaignStatusEnum::FAILED,
        ]);

        $this->campaign->addErrorToMeta('title_generation_failed', "Failed generating Ebook Title. " . formatLogMessage($exception->getMessage()));

        throw new \Exception("Job title generate failed! " . $exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
