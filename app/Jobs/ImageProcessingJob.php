<?php

namespace App\Jobs;

use App\Enum\CampaignStatusEnum;
use App\Enum\ImageSources;
use App\Models\Campaign;
use App\Models\Section;
use App\Service\FinalizeImageService;
use App\Service\ImageGenerateFactory;
use http\Exception\InvalidArgumentException;
use Illuminate\Bus\Batchable;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;
use Throwable;

class ImageProcessingJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels, Batchable;

    /**
     * The timeout for the job in seconds (6 hours).
     *
     * @var int
     */
    public $timeout = 21600; // 6 hours in seconds


    protected $campaign;
    protected $section;

    /**
     * The number of times the job may be attempted.
     *
     * @var int
     */
    public $tries = 3; // Maximum 3 attempts

    /**
     * The number of seconds to wait before retrying the job.
     *
     * @var int|array
     */
    public $backoff = [30, 60, 90];

    public function __construct(Campaign $campaign, Section $section)
    {
        $this->campaign = $campaign;
        $this->section = $section;
    }

    /**
     * Execute the job.
     */
    public function handle(FinalizeImageService $optimizedImage): void
    {
        if ($this->section->getMeta("image_source")) {
            $this->campaign->log("Skipping generating image. Section " . $this->section->id . " image already exist");
            return;
        }
        $form = is_string($this->campaign->form)
            ? json_decode($this->campaign->form, true)
            : $this->campaign->form;

        $imageSources = $form['image_sources'];

        $this->campaign->log("Section's image source. Data: " . formatLogMessage($imageSources));

        $randomSourceEnum = $imageSources[array_rand($imageSources)];

        $this->campaign->log("Section's image source enum. Data: " . formatLogMessage($randomSourceEnum));

        $imageUrl = ImageGenerateFactory::create($randomSourceEnum ?? ImageSources::AI_IMAGES->value)->generateImage($this->campaign, $this->section);

        $this->campaign->log("Section's image URL. " . formatLogMessage($imageUrl));

        $optimizeImageUrl = $optimizedImage->handleImage($imageUrl, imageFileName($this->section->title));
        $this->campaign->log("Section's optimized image URL. " . formatLogMessage($optimizeImageUrl));


        try {
            $this->campaign->log("Updating Section's intro.");
            $updatedIntro = placeSectionImageAfterIntro($this->section->intro, $optimizeImageUrl, $this->section->title);

            $this->campaign->log("Updating Section's status.");

            $this->section->update([
                'intro' => $updatedIntro,
                'meta' => array_merge($this->section->meta ?? [], ['image_source' => $optimizeImageUrl])
            ]);
        } catch (\Exception $e) {
            $this->campaign->log("Failed to update section intro with image URL. Error: " . formatLogMessage($e->getMessage()));
            throw new \Exception('Image generation failed: Invalid URL response');
        }
    }

    public function failed(?Throwable $exception)
    {
        $this->campaign->log("Failed to generate image for section {$this->section->id}. Error: " . formatLogMessage($exception->getMessage()));
        $this->campaign->update([
            'status' => CampaignStatusEnum::IMAGE_PROCESSING_FAILED,
        ]);

        $this->campaign->addErrorToMeta('image_processing', $exception->getMessage());

        throw new \Exception("Section image generation failed " . $exception->getMessage(), Response::HTTP_INTERNAL_SERVER_ERROR);
    }
}
