<?php

namespace App\Providers;

use App\Models\Campaign;
use App\Models\Chapter;
use App\Models\Download;
use App\Models\ResearchEbookCampaign;
use App\Models\Section;
use App\Models\User;
use App\Models\Webhook;
use App\Observers\CampaignObserver;
use App\Observers\ChapterObserver;
use App\Observers\DownloadObserver;
use App\Observers\ResearchEbookCampaignObserver;
use App\Observers\SectionObserver;
use App\Observers\UpdateUserCreditWebhookObserver;
use App\Observers\UserObserver;
use App\Service\AIClientFactory;
use App\Service\AIModel\Contracts\AIClientInterface;
use App\Service\CampaignImageProcessingJobService;
use App\Service\Contracts\CampaignImageProcessingServiceInterface;
use GuzzleHttp\Client;
use GuzzleHttp\ClientInterface;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\ServiceProvider;
use Filament\Http\Responses\Auth\Contracts\LoginResponse;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Register any application services.
     */
    public function register(): void
    {
        $this->app->bind(AIClientInterface::class, function ($app, array $parameters) {
            $factory = new AIClientFactory();
            $aiModel = $parameters['aiModel'] ?? config('services.ai_model', 'openai');
            return $factory->getClient($aiModel);
        });

        $this->app->singleton(ClientInterface::class, function ($app) {
            return new Client([
                'timeout' => 10,
            ]);
        });

        Http::macro('scrapeowl', function () {
            return Http::baseUrl(config('services.scrapeowl.url_v1'));
        });

        $this->app->bind(
            CampaignImageProcessingServiceInterface::class,
            CampaignImageProcessingJobService::class
        );
        
        $this->app->singleton(
            LoginResponse::class,
            \App\Http\Responses\LoginResponse::class,
        );
    }

    /**
     * Bootstrap any application services.
     */
    public function boot(): void
    {
        Webhook::observe(UpdateUserCreditWebhookObserver::class);
        User::observe(UserObserver::class);
        Campaign::observe(CampaignObserver::class);
        Download::observe(DownloadObserver::class);
        Section::observe(SectionObserver::class);
        Chapter::observe(ChapterObserver::class);
        ResearchEbookCampaign::observe(ResearchEbookCampaignObserver::class);
    }
}
