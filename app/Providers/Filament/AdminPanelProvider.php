<?php

namespace App\Providers\Filament;

use App\Filament\Pages\Login;
use App\Filament\Pages\MyProfile;
use App\Models\SiteSetting;
use Filament\Http\Middleware\Authenticate;
use Filament\Http\Middleware\DisableBladeIconComponents;
use Filament\Http\Middleware\DispatchServingFilamentEvent;
use Filament\Navigation\MenuItem;
use Filament\Pages;
use Filament\Panel;
use Filament\PanelProvider;
use Filament\Support\Colors\Color;
use Filament\Widgets;
use Illuminate\Cookie\Middleware\AddQueuedCookiesToResponse;
use Illuminate\Cookie\Middleware\EncryptCookies;
use Illuminate\Foundation\Http\Middleware\VerifyCsrfToken;
use Illuminate\Routing\Middleware\SubstituteBindings;
use Illuminate\Session\Middleware\AuthenticateSession;
use Illuminate\Session\Middleware\StartSession;
use Illuminate\View\Middleware\ShareErrorsFromSession;

class AdminPanelProvider extends PanelProvider
{
    public function panel(Panel $panel): Panel
    {
        SiteSetting::setCachedSettings();
        $favicon = SiteSetting::getFavicon() ?? asset('favicon.png');

        return $panel
            ->default()
            ->id('app')
            ->path('/')
            ->login(Login::class)
            ->passwordReset()
            ->favicon($favicon)
            ->brandName(fn() => config('site-settings')['name'] ?? "eBookWriter")
            ->colors(fn() => setThemeColors())
            ->topNavigation(config('site-settings')['has_top_navigation'] ?? false)
            ->sidebarCollapsibleOnDesktop(config('site-settings')['is_sidebar_collapse'] ?? true)
            // ->emailVerification(Pages\Auth\EmailVerification\EmailVerificationPrompt::class)
            ->profile()
            ->discoverResources(in: app_path('Filament/Resources'), for: 'App\\Filament\\Resources')
            ->discoverPages(in: app_path('Filament/Pages'), for: 'App\\Filament\\Pages')
            ->pages([
                // Pages\Dashboard::class,
            ])
            ->discoverWidgets(in: app_path('Filament/Widgets'), for: 'App\\Filament\\Widgets')
            ->widgets([
                Widgets\AccountWidget::class,
                Widgets\FilamentInfoWidget::class,
            ])
            ->viteTheme([
                'resources/css/filament/admin/theme.css',
            ])
            ->userMenuItems([
                'profile' => MenuItem::make()->url(fn(): string => MyProfile::getUrl()),
                MenuItem::make()
                    ->label('Become an affiliate')
                    ->url('https://warriorplus.com/aff-offer/o/bz4trh')
                    ->icon('heroicon-o-share'),
            ])
            ->middleware([
                EncryptCookies::class,
                AddQueuedCookiesToResponse::class,
                StartSession::class,
                AuthenticateSession::class,
                ShareErrorsFromSession::class,
                VerifyCsrfToken::class,
                SubstituteBindings::class,
                DisableBladeIconComponents::class,
                DispatchServingFilamentEvent::class,
            ])
            ->authMiddleware([
                Authenticate::class,
            ]);
            // ->viteTheme('resources/css/theme.css');
    }
}
