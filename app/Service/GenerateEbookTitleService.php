<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class GenerateEbookTitleService
{
    protected $aiClient;

//    public function __construct(AIClientInterface $aiClient)
//    {
//        $this->aiClient = $aiClient;
//    }

    public function execute(Campaign $campaign)
    {
        try {
            $titleResponse = $this->generateTitle($campaign);
            $campaign->log("Generate title response: " . formatLogMessage($titleResponse));

            if(!$titleResponse){
                $campaign->update([
                    'status' => CampaignStatusEnum::FAILED,
                ]);

                $campaign->addErrorToMeta('ebook_title_failed', "Generate title response: " . formatLogMessage($titleResponse));

                throw new \Exception("Unable to generate title");
            }

            $titleData = formatAIResponse($titleResponse);
            $campaign->log("Formatted Generated title response: " . formatLogMessage($titleData));

            if (isset($titleData['title'])) {
                $title = trim($titleData['title']);
                $campaign->log("Updating campaign status");
                $campaign->update([
                    'title' => $title,
                    'status' => CampaignStatusEnum::IN_PROGRESS,
                ]);
            } else {
                $campaign->log("Title key not found in the AI response. " . formatLogMessage($titleData));
            }
        } catch (\Exception $e) {
            $campaign->log("Failed to update title. Error: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }


    /**
     * @param  Campaign  $campaign
     *
     * @return string
     */
    public function generateTitle(Campaign $campaign): string
    {
        try {
            $campaignData = [
                'topic' => $campaign->topic,
                'context' => $campaign->getForm("context"),
                'purpose' => $campaign->getForm("purpose"),
                'audience' => $campaign->getForm("audience"),
                'tone' => $campaign->getForm("tone"),
                'language' => $campaign->getForm("language")
            ];

            $campaign->log("Campaign data for generate ebook title prompt " . json_encode($campaignData));

            $prompt = promptBuilder(prompts()['ebook_title'], $campaignData);
            $prompt .= prompts()['ignore_terms'];

            $campaign->log("Prompt for generate ebook title " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $campaign->log("Failed to generate ebook title from AI model. " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error in generating ebook title.' . $e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }

}
