<?php

namespace App\Service;

use App\Enum\CampaignTypeEnum;
use App\Models\Campaign;
use InvalidArgumentException;

class CampaignContextGeneratorFactory
{
    public static function make(Campaign $campaign)
    {
        return match ($campaign->type) {
            CampaignTypeEnum::INFORMATIONAL_POST->value => app(GenerateContextByInformationalPostService::class, ['campaign' => $campaign]),
            CampaignTypeEnum::YOUTUBE->value => app(GenerateContextByYoutubeVideoPostService::class, ['campaign' => $campaign]),
            default => throw new InvalidArgumentException("Unsupported campaign type for context generation: {$campaign->type}"),
        };
    }
}
