<?php

namespace App\Service;

use App\Enum\ResearchEbookCampaignIdeaTypeEnum;

class ResearchEbookCampaignServiceFactory
{
    public static function create(ResearchEbookCampaignIdeaTypeEnum $type)
    {
        return match ($type) {
            ResearchEbookCampaignIdeaTypeEnum::BOOK_TITLE_GENERATION => app(EbookBookTitleIdeaGenerationService::class),
            ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR => app(RandomEbookGenerateService::class),
            ResearchEbookCampaignIdeaTypeEnum::BEST_KEYWORD_CATEGORIES_FOR_EBOOK => app(EbookBestKeywordCategoryIdeaGenerationService::class),
            ResearchEbookCampaignIdeaTypeEnum::POPULAR_BOOKS_IN_NICHE => app(PopularBookInNicheService::class),
            ResearchEbookCampaignIdeaTypeEnum::GENERATE_AUTHOR_BIO => app(AuthorBioGenerationService::class),
            default => throw new \Exception("No service found for type: $type->value"),
        };
    }
}
