<?php

namespace App\Service\AIModel;

use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;
use OpenAI as PHPOpenAI;


class OpenAIClient implements AIClientInterface
{
    public function callAIModel(string $prompt, mixed $apiCredential, $model = "gpt-4o-mini", int $maxTokens = null): string
    {

        if (!$apiCredential){
            Log::debug("Open AI api key not found");
            throw new \Exception('Open AI api key not found.');
        }

        try {
            $openAI = PHPOpenAI::client($apiCredential);
            $response = $openAI->chat()->create([
                'model' => $model,
                'messages' => [
                    ['role' => 'system', 'content' => 'You are a helpful assistant.'],
                    ['role' => 'user', 'content' => $prompt],
                ],
                'max_tokens' => $maxTokens, // Default to 50 if not provided
            ]);
            return $response['choices'][0]['message']['content'];

        } catch (\Exception $e) {
            Log::error("Error calling OpenAI API: " . $e->getMessage());
            throw new \RuntimeException('There was an issue communicating with the OpenAI service. Please try again later.' . $e->getMessage());
        }
    }

    public function generateImage(string $prompt, $apiCredential, $model = "dall-e-3", string $size = '1024x1024', int $n = 1)
    {
        try {
            $openAI = PHPOpenAI::client($apiCredential);

            // Build base payload
            $payload = [
                'model' => $model,
                'prompt' => $prompt,
                'n' => $n,
                'size' => $size,
            ];

            // Add response_format only if using DALL·E
            if ($model === "dall-e-3") {
                $payload['response_format'] = 'url';
            }

            $response = $openAI->images()->create($payload);

            // Handle DALL·E (URL response)
            if ($model === "dall-e-3") {
                if (isset($response['data'][0]['url'])) {
                    Log::info("Generated image URL: " . $response['data'][0]['url']);
                    return $response['data'][0]['url'];
                }
            }

            // Handle GPT-4o (base64 image)
            if ($model === "gpt-image-1" && isset($response['data'][0]['b64_json'])) {
                $imageData = base64_decode($response['data'][0]['b64_json']);

                // Save to temp file
                $tempPath = storage_path('app/tmp_image_' . uniqid() . '.jpg');
                file_put_contents($tempPath, $imageData);

                // Upload to S3
                $filename = 'ai_ebook_images/' . uniqid() . '.jpg';
                Storage::disk('s3')->put($filename, fopen($tempPath, 'r+'), 'public');

                // Remove temp file
                unlink($tempPath);

                // Return public S3 URL
                $url = Storage::disk('s3')->url($filename);
                Log::info("Image uploaded to S3: $url");
                return $url;
            }

            Log::warning("No image data found in OpenAI response: " . json_encode($response));
            throw new \RuntimeException('Failed to generate image. No usable data returned.');

        } catch (\Exception $e) {
            Log::error("Image generation failed: " . $e->getMessage());
            throw new \RuntimeException('There was an issue generating the image. ' . $e->getMessage());
        }
    }

    public function generateCoverImage(string $prompt, $apiCredential, string $size = '1024x1536', int $n = 1)
    {
        try {
            $model = ($size === "1024x1792") ? "dall-e-3" : "gpt-image-1";

            $openAI = PHPOpenAI::client($apiCredential);

            // Build base payload
            $payload = [
                'model' => $model,
                'prompt' => $prompt,
                'n' => $n,
                'size' => $size,
            ];

            // Add response_format only if using DALL·E
            if ($model === "dall-e-3") {
                $payload['response_format'] = 'url';
            }

            $response = $openAI->images()->create($payload);

            // Handle DALL·E (URL response)
            if ($model === "dall-e-3") {
                if (isset($response['data'][0]['url'])) {
                    Log::info("Generated image URL: " . $response['data'][0]['url']);
                    return $response['data'][0]['url'];
                }
            }

            // Handle GPT-4o (base64 image)
            if ($model === "gpt-image-1" && isset($response['data'][0]['b64_json'])) {
                $imageData = base64_decode($response['data'][0]['b64_json']);

                // Save to temp file
                $tempPath = storage_path('app/tmp_image_' . uniqid() . '.jpg');
                file_put_contents($tempPath, $imageData);

                // Upload to S3
                $filename = 'ai_cover_images/' . uniqid() . '.jpg';
                Storage::disk('s3')->put($filename, fopen($tempPath, 'r+'), 'public');

                // Remove temp file
                unlink($tempPath);

                // Return public S3 URL
                $url = Storage::disk('s3')->url($filename);
                Log::info("Image uploaded to S3: $url");
                return $url;
            }

            Log::warning("No image data found in OpenAI response: " . json_encode($response));
            throw new \RuntimeException('Failed to generate image. No usable data returned.');

        } catch (\Exception $e) {
            Log::error("Image generation failed: " . $e->getMessage());
            throw new \RuntimeException('There was an issue generating the image. ' . $e->getMessage());
        }
    }


    public function generateAudio(string $text, $apiCredential, string $voice = 'alloy', string $model = 'tts-1', string $format = 'mp3')
    {
        try {
            if (!$apiCredential) {
                Log::debug("OpenAI API key not found");
                throw new \Exception('OpenAI API key not found.');
            }

            $openAI = PHPOpenAI::client($apiCredential);
            $response = $openAI->audio()->speech([
                'model' => $model,
                'input' => $text,
                'voice' => $voice,
                'response_format' => $format,
            ]);
            // dd($response);
            // if (!isset($response['data'])) { // Correct way to check if response contains valid data
            //     Log::error("Invalid response from OpenAI API: " . json_encode($response));
            //     throw new \RuntimeException('Failed to generate audio. Please try again later.');
            // }

            return $response; // Returns binary audio data

        } catch (\Exception $e) {
            Log::error("Error generating audio with OpenAI API: " . $e->getMessage());
            throw new \RuntimeException('There was an issue generating the audio. Please try again later.');
        }
    }

}
