<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\Chapter;
use App\Models\Section;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class GenerateSectionContentService
{
    protected $aiClient;

//    public function __construct(AIClientInterface $aiClient)
//    {
//        $this->aiClient = $aiClient;
//    }

    public function execute(int $chapterId, int $sectionId)
    {
        $section = Section::find($sectionId);
        $chapter = Chapter::find($chapterId);
        try {
            $chapter->campaign->log("Generating content");
            $contentResponse = $this->generateContent($chapterId, $sectionId);

            $chapter->campaign->log("Formatting Generated content");
            $content = formatAIResponseHtml($contentResponse);
            $chapter->campaign->log("Updating section status");

            $config = \HTMLPurifier_Config::createDefault();
            $purifier = new \HTMLPurifier($config);
            $cleanHtml = $purifier->purify($content);

            $section->update([
                'body' => $cleanHtml,
                'section_total_words' => $section->section_total_words + str_word_count(strip_tags($content)),
                "status" => CampaignStatusEnum::DONE
            ]);

            $campaign = Campaign::find($chapter->campaign_id);

            $campaign->log("Updating campaign total word length: GenerateSectionContentService: {$campaign->total_word_length} + {$section->section_total_words}");

            $campaign->update([
                "total_word_length" => $campaign->total_word_length +  $section->section_total_words
            ]);

        } catch (\Exception $e){
            $chapter->campaign->log("Failed to generate section content from AI model. Error: ".formatLogMessage($e->getMessage()));
            $campaign->addErrorToMeta('ai_model_error', "Failed to generate section content from AI model. Error: " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error in generating section content', Response::HTTP_BAD_REQUEST);
        }
    }

    /**
     * @param  Campaign  $campaign
     *
     * @return string
     */
    private function generateContent(int $chapterId, int $sectionId): string
    {
        $section = Section::find($sectionId);
        $chapter = Chapter::find($chapterId);
        $campaign = $chapter->campaign;
        $totalSections = $campaign->sections()->count();
        $totalDoneSections = $campaign->sections()->where('sections.status', CampaignStatusEnum::DONE)->count();
        $requiredWords = $campaign->required_word_length;
        $totalWordsWritten = $campaign->total_word_length;

        $plan = $campaign->ebook_plan;


        try {
            $campaignData = [
                'topic' => $campaign->topic,
                'title' => $campaign->title,
                'chapter_title' => $chapter->title,
                'chapter_intro' => $chapter->intro,
                'section_title' => $section->title,
                'section_intro' => $section->intro,
                'key_points' => $section->key_points,
                'context' => $campaign->getForm("context"),
                'purpose' => $campaign->getForm("purpose"),
                'audience' => $campaign->getForm("audience"),
                'tone' => $campaign->getForm("tone"),
                'language' => $campaign->getForm("language"),
                'required_words' => $requiredWords,
                "totalWordsWritten" => $totalWordsWritten,
                "totalSections" => $totalSections,
                "totalDoneSections" => $totalDoneSections,
            ];

            if (is_array($plan)) {
                $campaignData = array_merge($campaignData, $plan);
            }

            $chapter->campaign->log("Campaign data for generate section content prompt " . json_encode($campaignData));

            $prompt = promptBuilder(prompts()['section_keypoint_content'], $campaignData);

            $chapter->campaign->log("Prompt for generate section content " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $chapter->campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $chapter->campaign->log("Failed to generate section content from AI model. " . formatLogMessage($e->getMessage()));

            throw new \Exception('Error in generating section content', Response::HTTP_BAD_REQUEST);
        }
    }

}
