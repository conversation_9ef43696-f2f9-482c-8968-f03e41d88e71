<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\ResearchCampaignEntry;
use App\Models\ResearchEbookCampaign;
use App\Service\AIModel\Contracts\AIClientInterface;
use Symfony\Component\HttpFoundation\Response;

class RandomEbookGenerateService
{
    protected $aiClient;

    public function generate(ResearchEbookCampaign $campaign)
    {
        try {
            $titleResponse = $this->generateTitles($campaign);
            $campaign->log("Generate titles response: " . formatLogMessage($titleResponse));

            if(!$titleResponse){
                $campaign->update([
                    'status' => CampaignStatusEnum::FAILED,
                ]);
                $campaign->addErrorToMeta('generate_random_details', "Generate random details response: " . formatLogMessage($titleResponse));

                throw new \Exception("Unable to generate titles");
            }

            $titleData = formatAIResponse($titleResponse);
            $campaign->log("Formatted Generated title response: " . formatLogMessage($titleData));

            if (is_array($titleData)) {
                ResearchCampaignEntry::create([
                    'entry' => $titleData["title"],
                    "entry_content" => $titleData,
                    "research_ebook_campaign_id" => $campaign->id
                ]);
                return true;
            } else {
                $campaign->log("book_titles key not found in the AI response. " . formatLogMessage($titleData));
                return false;
            }
        } catch (\Exception $e) {
            $campaign->log("Failed to update title. Error: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    public function generateTitles(ResearchEbookCampaign $campaign): string
    {
        try {
            $campaignData = [
                'title' => $campaign->getForm("research_ebook_titles"),
            ];

            $campaign->log("Campaign data for generate random ebook context, KDP description, Author name and keywords prompt " . json_encode($campaignData));

            $prompt = promptBuilder(prompts()['random_ebook_details'], $campaignData);

            $campaign->log("Prompt for generate ebook titles " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $campaign->log("Failed to generate ebook titles from AI model. " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error in generating ebook titles.' . $e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
}
