<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\PopularBookInNicheEntry;
use App\Models\ResearchCampaignEntry;
use App\Models\ResearchEbookCampaign;
use App\Service\AIModel\Contracts\AIClientInterface;
use Symfony\Component\HttpFoundation\Response;

class PopularBookInNicheService
{
    protected $aiClient;
    public function generate(ResearchEbookCampaign $campaign)
    {
        try {
            $response = $this->generatePopularBooksInNiche($campaign);
            $campaign->log("Generate poppular book in niche: " . formatLogMessage($response));

            if(!$response){
                $campaign->update([
                    'status' => CampaignStatusEnum::FAILED,
                ]);
                $campaign->addErrorToMeta('popular_book_failed', "Generate poppular book in niche: " . formatLogMessage($response));

                throw new \Exception("Unable to generate popular books in niche");
            }

            $popularBooksInNiche = formatAIResponse($response);

            $campaign->log("Formatted Generated title response: " . formatLogMessage($popularBooksInNiche));

            if (is_array($popularBooksInNiche)) {
                foreach ($popularBooksInNiche as $popularBook) {
                    PopularBookInNicheEntry::create([
                        'author' => $popularBook["author"],
                        'book_name' => $popularBook["book_name"],
                        'summary' => $popularBook["summary"],
                        'key_takeways' => $popularBook["key_takeaways"] ?? $popularBook["key_takeways"] ?? null ,
                        "research_ebook_campaign_id" => $campaign->id
                    ]);
                }
                return true;
            } else {
                $campaign->log("response is not array in the AI response. " . formatLogMessage($popularBooksInNiche));
                return false;
            }
        } catch (\Exception $e) {
            $campaign->log("Failed to update popular book in niche. Error: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    /**
     * @param  Campaign  $campaign
     *
     * @return string
     */
    public function generatePopularBooksInNiche(ResearchEbookCampaign $campaign): string
    {
        try {
            $campaignData = [
                'niche' => $campaign->getForm("niche"),
                'publishing_year_range' => $campaign->getForm("publishing_year_range"),
                'platform' => $campaign->getForm("platform"),
                'target_market' => $campaign->getForm("target_market"),
                'success_metrics' => $campaign->getForm("success_metrics"),
                'limit' => $campaign->getForm("limit")
            ];

            $campaign->log("Campaign data for generate popular books in niche prompt " . json_encode($campaignData));

            $prompt = promptBuilder(prompts()['popular_books_in_niche'], $campaignData);

            $campaign->log("Prompt for generate popular books in niche " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $campaign->log("Failed to generate popular books in niche from AI model. " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error in generating popular books in niche.' . $e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
}
