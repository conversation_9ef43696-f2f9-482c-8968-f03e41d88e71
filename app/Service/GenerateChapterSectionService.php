<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\Chapter;
use App\Models\Section;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class GenerateChapterSectionService
{
    protected $aiClient;

//    public function __construct(AIClientInterface $aiClient)
//    {
//        $this->aiClient = $aiClient;
//    }

    /**
     * @param  Campaign  $campaign
     * @param  Chapter  $chapter
     *
     * @return void
     * @throws \Exception
     */
    public function execute(Campaign $campaign, Chapter $chapter)
    {
        $attempts = 0;
        $maxAttempts = 3;

        while ($attempts < $maxAttempts) {
            DB::beginTransaction();
            try {
                $attempts++;

                // Step 1: Generate sections
                $sectionResponse = $this->generateSections($campaign, $chapter);
                $campaign->log("Generate chapter's sections response: " . formatLogMessage($sectionResponse));
                $sections = formatAIResponse($sectionResponse);
                $campaign->log("Formatted Generated chapter's sections response: " . formatLogMessage($sections));

                $totalWordsToAdd = 0;

                // Step 2: Initialize HTML purifier
                $config = \HTMLPurifier_Config::createDefault();
                $purifier = new \HTMLPurifier($config);

                // Step 3: Loop through and create sections
                foreach ($sections as $section) {
                    $cleanSectionTitle = $purifier->purify($section['section_title']);
                    $cleanSectionIntro = $purifier->purify($section['section_intro']);

                    $campaign->log("Creating chapter's section to db");

                    $newSection = Section::create([
                        'chapter_id' => $chapter->id,
                        'title' => $cleanSectionTitle,
                        'intro' => $cleanSectionIntro,
                        'status' => CampaignStatusEnum::IN_PROGRESS,
                        'key_points' => json_encode($section['key_points']),
                        'section_total_words' => str_word_count(strip_tags($section['section_title'])) + str_word_count(strip_tags($section['section_intro'])),
                    ]);

                    $totalWordsToAdd += $newSection->section_total_words;
                }

                // Step 4: Update campaign's total word length within the same transaction
                $campaign->log("Updating campaign total word length after commit: GenerateChapterSectionService: {$campaign->total_word_length} + {$totalWordsToAdd}");
                $campaign->total_word_length += $totalWordsToAdd;
                $campaign->save();

                // Step 5: Commit the transaction
                DB::commit();

                // Success, exit the loop
                return;

            } catch (\Exception $e) {
                DB::rollBack();
                $campaign->log("Failed to generate sections. Error: " . formatLogMessage($e->getMessage()));

                // Retry after a short delay
                sleep(1);
            }
        }

        // If we reach this point, it means all retries failed
        throw new \Exception("Failed to generate sections after {$maxAttempts} attempts.");
    }



    /**
     * @param  Campaign  $campaign
     * @param  Chapter  $chapter
     *
     * @return string
     */
    private function generateSections(Campaign $campaign, Chapter $chapter): string
    {
        $plan = $campaign->ebook_plan;
        $requiredWords = $campaign->required_word_length;
        $totalChapters = $campaign->chapters()->count();
        try {
            $campaignData = [
                'topic' => $campaign->topic,
                'title' => $campaign->title,
                'chapter_title' => $chapter->title,
                'chapter_intro' => $chapter->intro,
                'context' => $campaign->getForm("context"),
                'purpose' => $campaign->getForm("purpose"),
                'audience' => $campaign->getForm("audience"),
                'tone' => $campaign->getForm("tone"),
                'language' => $campaign->getForm("language"),
                'required_words' => $requiredWords,
                'totalChapters' => $totalChapters,
            ];

            if (is_array($plan)) {
                $campaignData = array_merge($campaignData, $plan);
            }

            $prompt = promptBuilder(prompts()['ebook_sections'], $campaignData);
            $prompt .= prompts()['ignore_terms'];

            $campaign->log("Prompt for generate sections with key points " . formatLogMessage($prompt));
            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $campaign->log("Failed to generate sections from AI model. Error: " . formatLogMessage($e->getMessage()));
            $campaign->addErrorToMeta('ai_model_error', "Failed to generate sections from AI model. Error: " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error in generating sections', Response::HTTP_BAD_REQUEST);
        }
    }

}
