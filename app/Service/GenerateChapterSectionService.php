<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\Chapter;
use App\Models\Section;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Symfony\Component\HttpFoundation\Response;

class GenerateChapterSectionService
{
    protected $aiClient;

//    public function __construct(AIClientInterface $aiClient)
//    {
//        $this->aiClient = $aiClient;
//    }

    /**
     * @param  Campaign  $campaign
     * @param  Chapter  $chapter
     *
     * @return void
     * @throws \Exception
     */
    public function execute(Campaign $campaign, Chapter $chapter)
    {
        DB::beginTransaction();
        try {
            $sectionResponse = $this->generateSections($campaign, $chapter);
            $campaign->log("Generate chapter's sections response: " . formatLogMessage($sectionResponse));
            $sections = formatAIResponse($sectionResponse);
            $campaign->log("Formatted Generated chapter's sections response: " . formatLogMessage($sections));

            $totalWordsToAdd = 0;

            $config = \HTMLPurifier_Config::createDefault();
            $purifier = new \HTMLPurifier($config);

            foreach ($sections as $section) {

                $cleanSectionTitle = $purifier->purify($section['section_title']);
                $cleanSectionIntro = $purifier->purify($section['section_intro']);

                $campaign->log("Creating chapter's section to db");
                $section = Section::create([
                    'chapter_id' => $chapter->id,
                    'title' => $cleanSectionTitle,
                    'intro' => $cleanSectionIntro,
                    'status' => CampaignStatusEnum::IN_PROGRESS,
                    'key_points' => json_encode($section['key_points']),
                    'section_total_words' => str_word_count(strip_tags($section['section_title'])) + str_word_count(strip_tags($section['section_intro'])),
                ]);

                $totalWordsToAdd += $section->section_total_words;
                $campaign->log("Creating chapter's section to db");
            }
            DB::commit();

            $campaign = Campaign::find($campaign->id);
            $campaign->log("Updating campaign total word length after commit: GenerateChapterSectionService: {$campaign->total_word_length} + {$totalWordsToAdd}");
            $campaign->update([
                "total_word_length" => $campaign->total_word_length + $totalWordsToAdd,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();
            $campaign->log("Failed to generate sections. Error: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }


    /**
     * @param  Campaign  $campaign
     * @param  Chapter  $chapter
     *
     * @return string
     */
    private function generateSections(Campaign $campaign, Chapter $chapter): string
    {
        $plan = $campaign->ebook_plan;
        $requiredWords = $campaign->required_word_length;
        $totalChapters = $campaign->chapters()->count();
        try {
            $campaignData = [
                'topic' => $campaign->topic,
                'title' => $campaign->title,
                'chapter_title' => $chapter->title,
                'chapter_intro' => $chapter->intro,
                'context' => $campaign->getForm("context"),
                'purpose' => $campaign->getForm("purpose"),
                'audience' => $campaign->getForm("audience"),
                'tone' => $campaign->getForm("tone"),
                'language' => $campaign->getForm("language"),
                'required_words' => $requiredWords,
                'totalChapters' => $totalChapters,
            ];

            if (is_array($plan)) {
                $campaignData = array_merge($campaignData, $plan);
            }

            $prompt = promptBuilder(prompts()['ebook_sections'], $campaignData);
            $prompt .= prompts()['ignore_terms'];

            $campaign->log("Prompt for generate sections with key points " . formatLogMessage($prompt));
            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $campaign->log("Failed to generate sections from AI model. Error: " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error in generating sections', Response::HTTP_BAD_REQUEST);
        }
    }

}
