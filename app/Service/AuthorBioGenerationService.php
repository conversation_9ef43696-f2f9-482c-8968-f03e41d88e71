<?php

namespace App\Service;

use App\Enum\CampaignStatusEnum;
use App\Models\Campaign;
use App\Models\ResearchCampaignEntry;
use App\Models\ResearchEbookCampaign;
use App\Service\AIModel\Contracts\AIClientInterface;
use Symfony\Component\HttpFoundation\Response;

class AuthorBioGenerationService
{
    protected $aiClient;
    public function generate(ResearchEbookCampaign $campaign)
    {
        try {
            $response = $this->generateAuthorBio($campaign);
            $campaign->log("Generate author bio response: " . formatLogMessage($response));

            if(!$response){
                $campaign->update([
                    'status' => CampaignStatusEnum::FAILED,
                ]);
                $campaign->addErrorToMeta('generate_author_bio_failed', "Generate author bio response: " . formatLogMessage($response));

                throw new \Exception("Unable to generate author bio");
            }

            $authorBios = formatAIResponse($response);
            $campaign->log("Formatted Generated best keyword categories response: " . formatLogMessage($authorBios));

            if (is_array($authorBios)) {
                foreach ($authorBios as $authorBio) {
                    ResearchCampaignEntry::create([
                        'entry' => $authorBio['author_bio'],
                        "research_ebook_campaign_id" => $campaign->id
                    ]);
                }

                return true;
            } else {
                $campaign->log("author_bio key not found in the AI response. " . formatLogMessage($authorBios));
                return false;
            }
        } catch (\Exception $e) {
            $campaign->log("Failed to update author bio. Error: " . formatLogMessage($e->getMessage()));
            throw $e;
        }
    }

    /**
     * @param  Campaign  $campaign
     *
     * @return string
     */
    public function generateAuthorBio(ResearchEbookCampaign $campaign): string
    {
        try {
            $campaignData = [
                'author_name' => $campaign->getForm("author_name"),
                'speciality' => $campaign->getForm("speciality"),
                'notable_works' => $campaign->getForm("notable_works"),
                'professional_background' => $campaign->getForm("professional_background"),
                'tone' => $campaign->getForm("tone"),
                'limit' => $campaign->getForm("limit"),
            ];

            $campaign->log("Campaign data for generate author bio prompt " . json_encode($campaignData));

            $prompt = promptBuilder(prompts()['author_bio'], $campaignData);

            $campaign->log("Prompt for generate author bio " . formatLogMessage($prompt));

            $this->aiClient = app(AIClientInterface::class, ['aiModel' => $campaign->ai_model]);
            return $this->aiClient->callAIModel($prompt, $campaign->getAiApiKey($campaign->ai_model), $campaign->ai_model);
        } catch (\Exception $e){
            $campaign->log("Failed to generate author bio from AI model. " . formatLogMessage($e->getMessage()));
            throw new \Exception('Error in generating author bio.' . $e->getMessage(), Response::HTTP_BAD_REQUEST);
        }
    }
}
