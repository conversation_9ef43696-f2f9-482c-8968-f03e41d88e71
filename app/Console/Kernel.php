<?php

namespace App\Console;

use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * Define the application's command schedule.
     */
    protected function schedule(Schedule $schedule): void
    {
        $schedule->command('clear:audio-books')->daily();
        $schedule->command('app:renew-recurring-ltd-monthly-subscriptions')->daily();
        $schedule->command('campaigns:retry-stuck')->everyTenMinutes();
        $schedule->command('campaigns:clear-stuck-merge-flags')->everyFifteenMinutes();
    }

    /**
     * Register the commands for the application.
     */
    protected function commands(): void
    {
        $this->load(__DIR__ . '/Commands');

        require base_path('routes/console.php');
    }
}
