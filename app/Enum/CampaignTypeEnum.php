<?php

namespace App\Enum;

use App\Models\SiteSetting;
use Filament\Support\Contracts\HasLabel;

enum CampaignTypeEnum: string implements HasLabel
{
    case DEFAULT = 'default';
    case INFORMATIONAL_POST = 'informational_post';
    case YOUTUBE = 'youtube_video';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::DEFAULT => "Instant eBook",
            self::INFORMATIONAL_POST => "URL to eBook",
            self::YOUTUBE => "Video to eBook",
        };
    }

    static function getAllowedTypes(): array
    {
//        if (auth()->check() && !plan()?->hasPermissionToCampaigns()) {
//            return [];
//        }

        $types = [
            self::DEFAULT->value => self::DEFAULT->getLabel(),
        ];
        if(isCampaignTypeEnabled(FeatureEnum::VIDEO_TO_EBOOK->value) && auth()->user()->getUser()->hasVideoToEbook() ){
            $types[self::INFORMATIONAL_POST->value] = self::INFORMATIONAL_POST->getLabel();
            $types[self::YOUTUBE->value] = self::YOUTUBE->getLabel();
        }

        return $types;
    }

    static function getShortTypes(): array
    {
        return [
            self::DEFAULT->value => 'Default',
            self::INFORMATIONAL_POST->value => 'Informational',
            self::YOUTUBE->value => 'YouTube',
        ];
    }
}


