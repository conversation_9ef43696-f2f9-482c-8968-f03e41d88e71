<?php

namespace App\Enum;

use Filament\Support\Contracts\HasLabel;

enum ResearchEbookCampaignIdeaTypeEnum: string implements HasLabel
{
    case BOOK_TITLE_GENERATION = 'book_title_generation';
    case POPULAR_BOOKS_IN_NICHE = 'popular_books_in_niche';
    case MARKET_DEMAND_ANALYSIS = 'market_demand_analysis';
    case GENERATE_AUTHOR_BIO = 'generate_author_bio';
    case BEST_KEYWORD_CATEGORIES_FOR_EBOOK = 'best_keyword_categories_for_ebook';
    case RANDOM_EBOOK_GENERATOR = 'random_ebook_generator';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::BOOK_TITLE_GENERATION => "Book Title Generation",
            self::POPULAR_BOOKS_IN_NICHE => "Popular Books in Niche",
            self::MARKET_DEMAND_ANALYSIS => "Market Demand Analysis",
            self::GENERATE_AUTHOR_BIO => "Generate Author Bio",
            self::BEST_KEYWORD_CATEGORIES_FOR_EBOOK => "Best Keywords & Categories for eBook",
            self::RANDOM_EBOOK_GENERATOR => "Random eBook Generator",
        };
    }

    public static function getAllowedTypes(): array
    {
        $types = [
            self::BOOK_TITLE_GENERATION->value => self::BOOK_TITLE_GENERATION->getLabel(),
            self::POPULAR_BOOKS_IN_NICHE->value => self::POPULAR_BOOKS_IN_NICHE->getLabel(),
            //self::MARKET_DEMAND_ANALYSIS->value => self::MARKET_DEMAND_ANALYSIS->getLabel(),
            self::GENERATE_AUTHOR_BIO->value => self::GENERATE_AUTHOR_BIO->getLabel(),
            self::BEST_KEYWORD_CATEGORIES_FOR_EBOOK->value => self::BEST_KEYWORD_CATEGORIES_FOR_EBOOK->getLabel(),
            self::RANDOM_EBOOK_GENERATOR->value => self::RANDOM_EBOOK_GENERATOR->getLabel(),
        ];

        return $types;
    }
}
