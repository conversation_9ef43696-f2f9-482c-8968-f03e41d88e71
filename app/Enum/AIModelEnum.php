<?php

namespace App\Enum;

enum AIModelEnum: string
{
    case OPEN_AI = 'openai';
    case OPEN_ROUTER = 'openrouter';

    // OpenAI models
    case GPT_3_5_TURBO = 'gpt-3.5-turbo';
    //case GPT_4 = 'gpt-4';
    case GPT_4_TURBO = 'gpt-4-turbo';
    case GPT_4O_MINI = 'gpt-4o-mini';
    case GPT_4O = 'gpt-4o';

    // OpenRouter models
    case OPENROUTER_DEEPSEEK_R1 = 'deepseek/deepseek-r1-distill-llama-8b';
    case OPENROUTER_OPENAI_GPT_45 = 'openai/gpt-4.5-preview'; // Proxy for "ChatGPT 4.5"
    case OPENROUTER_ANTHROPIC_CLAUDE_3_7_SONNET = 'anthropic/claude-3.7-sonnet';
    case OPENROUTER_XAI_GROK_2 = 'x-ai/grok-2-vision-1212';
    case OPENROUTER_META_LLAMA_33_70B_INSTRUCT = 'meta-llama/llama-3.3-70b-instruct';
    case OPENROUTER_GOOGLE_GEMINI_2_FLASH_001 = 'google/gemini-2.0-flash-001';

    /**
     * Get labels for providers and models
     */
    public static function getLabels(): array
    {
        return [
            self::OPEN_AI->value => 'Open AI',
            self::OPEN_ROUTER->value => 'OpenRouter',
            self::GPT_3_5_TURBO->value => 'GPT-3.5 Turbo',
            //self::GPT_4->value => 'GPT-4',
            self::GPT_4_TURBO->value => 'GPT-4 Turbo',
            self::GPT_4O_MINI->value => 'GPT-4o Mini',
            self::GPT_4O->value => 'GPT-4o',
            self::OPENROUTER_DEEPSEEK_R1->value => 'DeepSeek: R1',
            self::OPENROUTER_OPENAI_GPT_45->value => 'OpenAI: GPT-4.5 (Beta)', // Proxy for "ChatGPT 4.5"
            self::OPENROUTER_ANTHROPIC_CLAUDE_3_7_SONNET->value => 'Anthropic: Claude 3.7 Sonnet',
            self::OPENROUTER_XAI_GROK_2->value => 'xAI: Grok 2',
            self::OPENROUTER_GOOGLE_GEMINI_2_FLASH_001->value => 'Google: Gemini Flash 2.0',
            self::OPENROUTER_META_LLAMA_33_70B_INSTRUCT->value => 'Meta: Llama 3.3 70B Instruct',
        ];
    }

    /**
     * Get all OpenAI models (excluding the parent provider itself)
     */
    public static function getOpenAiModels(): array
    {
        return [
            self::GPT_3_5_TURBO->value,
            //self::GPT_4->value,
            self::GPT_4_TURBO->value,
            self::GPT_4O_MINI->value,
            self::GPT_4O->value,
        ];
    }

    /**
     * Get all OpenRouter models (excluding the parent provider itself)
     */
    public static function getOpenRouterModels(): array
    {
        return [
            self::OPENROUTER_DEEPSEEK_R1->value,
            self::OPENROUTER_OPENAI_GPT_45->value,
            self::OPENROUTER_ANTHROPIC_CLAUDE_3_7_SONNET->value,
            self::OPENROUTER_XAI_GROK_2->value,
            self::OPENROUTER_GOOGLE_GEMINI_2_FLASH_001->value,
            self::OPENROUTER_META_LLAMA_33_70B_INSTRUCT->value,
        ];
    }

    /**
     * Get the parent provider for a given model
     */
    public static function getProviderForModel(string $model): ?self
    {
        $model = strtolower($model);
        // Remove 'openrouter-' prefix if present
        $normalizedModel = str_starts_with($model, 'openrouter-') ? str_replace('openrouter-', '', $model) : $model;

        if (in_array($normalizedModel, self::getOpenAiModels())) {
            return self::OPEN_AI;
        }
        if (in_array($normalizedModel, self::getOpenRouterModels())) {
            return self::OPEN_ROUTER;
        }
        return match ($normalizedModel) {
            self::OPEN_AI->value => self::OPEN_AI,
            self::OPEN_ROUTER->value => self::OPEN_ROUTER,
            default => null,
        };
    }

}
