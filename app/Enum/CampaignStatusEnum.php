<?php

namespace App\Enum;

use Filament\Support\Contracts\HasColor;
use Filament\Support\Contracts\HasLabel;

enum CampaignStatusEnum: string implements HasLabe<PERSON>, HasColor
{
    case NEW                     = 'NEW'; // waiting for approval
    case PENDING                 = 'PENDING'; // waiting for approval
    case IN_PROGRESS             = 'IN PROGRESS';
    case IMAGE_PROCESSING        = 'IMAGE PROCESSING';
    case IMAGE_PROCESSING_FAILED = 'IMAGE PROCESSING FAILED';
    case AUDIO_PROCESSING        = 'AUDIO PROCESSING';
    case AUDIO_PROCESSING_FAILED        = 'AUDIO PROCESSING FAILED';

    case DONE                    = 'DONE';
    case DRAFT                   = 'DRAFT';
    case ACTIVE                  = 'ACTIVE';

    case PAUSED                  = 'PAUSED';

    case ERROR                   = 'ERROR';
    case FAILED                  = 'FAILED';
    case GENERATE_PDF            = 'GENERATE PDF';
    case GENERATE_EPUB           = 'GENERATE EPUB';

    public function getLabel(): ?string
    {
        return match ($this) {
            self::NEW => 'New',
            self::PENDING => 'Pending',
            self::IN_PROGRESS => 'In Progress',
            self::PAUSED => 'Paused',
            self::DONE => 'Done',
            self::ERROR => 'Error',
            self::FAILED => 'Failed',
            self::ACTIVE => 'Active',
            self::DRAFT => 'Draft',
            self::IMAGE_PROCESSING => 'Image Processing',
            self::IMAGE_PROCESSING_FAILED => 'Image Processing Failed',
            self::GENERATE_PDF => 'Generating PDF',
            self::GENERATE_EPUB => 'Generating EPUB',
            self::AUDIO_PROCESSING => 'Audio Processing',
            self::AUDIO_PROCESSING_FAILED => 'Audio Processing Failed',
        };
    }

    public function getColor(): string
    {
        return match ($this) {
            self::DRAFT, self::NEW => 'gray',
            self::ACTIVE, self::IN_PROGRESS, self::IMAGE_PROCESSING, self::GENERATE_EPUB, self::GENERATE_PDF, self::AUDIO_PROCESSING => 'info',
            self::PAUSED, self::PENDING => 'warning',
            self::DONE => 'success',
            self::ERROR, self::FAILED, self::IMAGE_PROCESSING_FAILED , self::AUDIO_PROCESSING_FAILED => 'danger',
        };
    }

}
