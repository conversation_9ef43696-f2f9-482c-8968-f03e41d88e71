<?php

namespace App\Actions;

use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Jobs\GenerateCoverImage;
use App\Jobs\GenerateEbookJob;
use Filament\Notifications\Notification;

class ReGenerateEbookAction
{
    public static function regenerateAction($campaign)
    {
        if (
            CreditDeductEnum::EBOOK_GENERATION_CREDIT->value > $campaign->user->remainCredit()
        ){
            $remaining = $campaign->user->remainCredit();
            $required = CreditDeductEnum::EBOOK_GENERATION_CREDIT->value;
            Notification::make()
                        ->title("You do not have enough credits. (Available: {$remaining}, Required: {$required})")
                        ->danger()
                        ->send();
            return;
        }

        $campaign->update([
            "status" => CampaignStatusEnum::IN_PROGRESS,
        ]);

        if($campaign->getForm("generate_ai_cover_image")){
            GenerateCoverImage::dispatch($campaign->id);
        }
        GenerateEbookJob::dispatch($campaign->id);
        // Notify the user of success
        Notification::make()
                    ->title("We're processing your ebook generation. Please wait a few moments and come back again.")
                    ->success()
                    ->send();
    }
}
