<?php

namespace App\Actions;

use App\Enum\CampaignStatusEnum;
use App\Jobs\DispatchChapterSectionsJob;
use App\Jobs\GenerateCampaignContextJob;
use App\Jobs\GenerateEbookChaptersJob;
use App\Jobs\GenerateEbookTitleJob;
use App\Jobs\GenerateSectionKeyPointContentJob;
use App\Models\Campaign;
use App\Service\CalculateEbookWordFormulaService;
use App\Service\CampaignImageProcessingJobService;
use Illuminate\Support\Facades\Bus;

class RunCampaignAction
{
    public function execute(Campaign $campaign, bool $retry = false): void
    {
        if (!$campaign->getOpenAiApiKey()){
            $campaign->log("OpenAI api key not found.");
            $campaign->update([
                "status" => CampaignStatusEnum::FAILED
            ]);
            $campaign->addErrorToMeta('open_ai_key_not_found', "OpenAI api key not found.");
            return;
        }

        $highestRequiredCredit = HighestRequiredCreditCalculator::getHighestRequiredCredits($campaign->page_length);

        if (!($campaign->user->remainCredit() >= $highestRequiredCredit)){
            $campaign->log("No enough credits to run campaign.");
            $campaign->update([
                "status" => CampaignStatusEnum::FAILED
            ]);
            $campaign->addErrorToMeta('low_credit', "No enough credits to run campaign.");
            return;
        }

        $campaign->log("Marking campaign status IN PROGRESS for campaign: {$campaign->id}");
        $this->calculateRequiredWords($campaign);
        $this->markCampaignInProgress($campaign);

        if ($retry && $this->hasPendingSections($campaign)) {

            $campaign->log("Campaign retry condition meet true and dispatching section jobs for campaign: {$campaign->id}");

            $this->dispatchSectionJobs($campaign);
            return;
        }

        if ($retry && $campaign->status == CampaignStatusEnum::IMAGE_PROCESSING_FAILED){
            $processImagesForCampaign = new CampaignImageProcessingJobService();
            $processImagesForCampaign->processImagesForCampaign($campaign);
            return;
        }

        $campaign->log("Dispatching all initial jobs for campaign: {$campaign->id}");

        $this->dispatchInitialJobs($campaign);
    }

    private function markCampaignInProgress(Campaign $campaign): void
    {
        $campaign->update(['status' => CampaignStatusEnum::IN_PROGRESS]);
    }

    private function hasPendingSections(Campaign $campaign): bool
    {
        return $campaign->sections->where('status', '!=', CampaignStatusEnum::DONE)->isNotEmpty();
    }

    private function dispatchSectionJobs(Campaign $campaign): void
    {

        $sectionJobs = $campaign->sections
            ->where('status', '!=', CampaignStatusEnum::DONE)
            ->map(function ($section) {
                return new GenerateSectionKeyPointContentJob($section->chapter->id, $section->id);
            })
            ->toArray();

        $campaign->log("Added section jobs to an array. Section Jobs: " . json_encode($sectionJobs));

        $sectionJobs[] = new DispatchChapterSectionsJob($campaign);
        $campaign->log("Dispatching Section Jobs: " . json_encode($sectionJobs) . " for campaign: {$campaign->id}");
        Bus::chain($sectionJobs)->dispatch();
    }

    private function dispatchInitialJobs(Campaign $campaign): void
    {
        $jobs = [
            new GenerateCampaignContextJob($campaign),
            new GenerateEbookTitleJob($campaign),
            new GenerateEbookChaptersJob($campaign),
        ];
        Bus::batch($jobs)
           ->onQueue("campaign")
           ->then(function () use ($campaign) {
               DispatchChapterSectionsJob::dispatch($campaign)->onQueue("campaign");
           })->dispatch();
    }

    private function calculateRequiredWords($campaign): void
    {
        $calculateWords = new CalculateEbookWordFormulaService();
        $requiredWords = $calculateWords->calculateWords($campaign->ebookFormat, $campaign->page_length);
        //dd($requiredWords, $campaign->ebookFormat);
        $campaign->update(['required_word_length' => $requiredWords]);

        $calculateWords->ebookPlan($campaign, $requiredWords);
    }

}
