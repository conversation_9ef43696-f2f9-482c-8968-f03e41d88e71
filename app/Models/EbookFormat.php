<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EbookFormat extends Model
{
    use HasFactory;

    protected $guarded = ["id"];

    /**
     * The attributes that should be cast.
     *
     * @var array
     */
    protected $casts = [
        'background_opacity' => 'float',
    ];

    /**
     * Get the background opacity value with a default of 1.0 if not set
     *
     * @return float
     */
    public function getBackgroundOpacityAttribute($value)
    {
        return $value ?? 1.0;
    }
}
