<?php

namespace App\Models;

use App\Console\Commands\UpdateCampaignStatuses;
use App\Enum\AIModelEnum;
use App\Enum\CampaignPlatformEnum;
use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Enum\CreditLogActionEnum;
use App\Enum\ImageSources;
use App\Enum\SiteFeaturesEnum;
use App\Jobs\FetchYoutubeJob;
use App\Jobs\GenerateCommentJob;
use App\Jobs\RunCampaignJob;
use App\Jobs\UpdateCampaignJob;
use App\Traits\HasCreditLog;
use App\Traits\HasLogger;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Support\Facades\Bus;
use Throwable;

class Campaign extends Model
{
    use HasLogger;
    use HasCreditLog;

    protected $guarded = [];

    protected $casts = [
        'status' => CampaignStatusEnum::class,
        'form' => 'json',
        'ebook_plan' => 'json',
        'meta' => 'json',
        'url' => 'json',
        'types' => 'array',
        'review_form_config' => 'json',
    ];

    protected static function boot(): void
    {
        parent::boot();

        static::creating(function ($content) {
            if (!$content->user_id) {
                $content->user_id = auth()->user()->id;
            }

            if (!$content->status) {
                $content->status = CampaignStatusEnum::PENDING->value;
            }

            // Set default review form configuration if not already set
            if (empty($content->review_form_config)) {
                $content->review_form_config = \App\Filament\Resources\CampaignResource\ReviewFormConfig::getDefaultConfig();
            }
        });

        static::created(function (Campaign $campaign) {
            // $campaign->cost = $campaign->user->campaignCreditCost($campaign->type->value, $campaign->limit);
            // $campaign->mw_cost = $campaign->limit * $campaign->type->microWorkerRatesWithMWFees();
            // if ($campaign->status === CampaignStatusEnum::NEW ) {
            //     $campaign->runOnQueue();
            // }
        });
        // static::deleted(function (Campaign $campaign) {
        //     try {
        //         $campaign->stop();
        //     } catch (Exception $e) {
        //         $campaign->log("Campaign stop failed. " . $e->getMessage());
        //     }
        // });
        // static::updated(function (Campaign $campaign) {
        //     if($campaign->wasChanged(['url','form',"limit","limit_per_day"])){
        //         $campaign->updatwMWCampaign();
        //     }
        // });
    }
    public function saveMeta($key, $value): void
    {
        try{
            $meta = $this->meta ?? [];
            $meta[$key] = $value;
            $this->meta = $meta;
            $this->save();
        }
        catch (\Exception $e){
            $this->log("Failed to save meta. " . formatLogMessage($e->getMessage()));
        }
    }

    /**
     * Recursively ensure all strings are valid UTF-8.
     */
    private function sanitizeUtf8($data)
    {
        if (is_string($data)) {
            if (!mb_detect_encoding($data, 'UTF-8', true)) {
                return mb_convert_encoding($data, 'UTF-8');
            }
            return $data;
        } elseif (is_array($data)) {
            foreach ($data as $k => $v) {
                $data[$k] = $this->sanitizeUtf8($v);
            }
            return $data;
        } elseif (is_object($data)) {
            foreach ($data as $k => $v) {
                $data->$k = $this->sanitizeUtf8($v);
            }
            return $data;
        }
        return $data;
    }

    public function saveForm($key, $value): void
    {
        $form = $this->form ?? [];
        $form[$key] = $value;
        $this->form = $form;
        $this->save();
    }

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    public function getOpenAiApiKey(): ?string
    {
        return $this->user?->getOpenAiApiKey();
    }

    public function getOpenRouterApiKey(): ?string
    {
        return $this->user?->getOpenRouterApiKey();
    }

    public function getAiApiKey(string $aiModel): ?string
    {
        $aiModel = strtolower($aiModel);
        $provider = AIModelEnum::getProviderForModel($aiModel);

        return match ($provider) {
            AIModelEnum::OPEN_AI => $this->getOpenAiApiKey(),
            AIModelEnum::OPEN_ROUTER => $this->getOpenRouterApiKey(),
            default => null,
        };
    }

    public function getMeta($key, $default = null)
    {
        try{
            return $this->meta[$key] ?? $default;
        }
        catch (\Exception $e){
            $this->log("Failed to get meta. " . formatLogMessage($e->getMessage()));
            return $default;
        }
    }

    public function downloads(): HasMany
    {
        return $this->hasMany(Download::class);
    }

    public function fileCount(string $type): int
    {
        return $this->downloads()->where("type", $type)->count();
    }


    public function getUrl($key, $default = null)
    {
        return $this->url[$key] ?? $default;
    }

    public function getEpub()
    {
        return $this->getUrl("epub") ?? null;
    }

    public function getPdf()
    {
        return $this->getUrl("pdf") ?? null;
    }

    public function getForm($key, $default = null)
    {
        return $this->form[$key] ?? $default;
    }

    public function getAffiliateLinks(): array
    {
        $links = $this->getForm('affiliate_links', []);

        if (!is_array($links) or count($links) < 1) {
            return [];
        }

        return collect($links)->mapWithKeys(function ($item) {
            return [$item['affiliate_link_keyword'] => $item['affiliate_link_url']];
        })->toArray();
    }

    public function getAffiliateLinkKeywords(): array
    {
        if (!is_array($this->getAffiliateLinks()) or count($this->getAffiliateLinks()) < 1) {
            return [];
        }
        return array_keys($this->getAffiliateLinks());
    }


    public function ebookFormat(): BelongsTo
    {
        return $this->belongsTo(EbookFormat::class);
    }

    public function chapters(): HasMany
    {
        return $this->hasMany(Chapter::class);
    }

    public function sections(): HasManyThrough
    {
        return $this->hasManyThrough(Section::class, Chapter::class);
    }

    /**
     * Get the reviews for the campaign.
     */
    public function reviews(): HasMany
    {
        return $this->hasMany(CampaignReview::class);
    }

    public function fail($message, $src = ""): void
    {
        $this->log("Campaign has been failed for" . $message . " --src=" . $src);
        $this->saveMeta('fail_message', $message);
        $this->update([
            'status' => CampaignStatusEnum::FAILED->value,
        ]);
    }

    public function deductCredit()
    {
        $completeTask = $this->meta['mw_completed_task'] ?? 0;
        $credit = $this->user->campaignCreditCost($this->type->value, $this->limit - $completeTask);
        $this->logCreditActivity(
            user: $this->user,
            action: CreditLogActionEnum::CAMPAIGN,
            credit: $credit,
            descPrefix: $this->id,
            logMeta: [
                'campaign_id' => $this->id,
            ],
        );
        $this->user->useCredit($credit);
        $this->log($credit . " credit deducted");
    }

    public function image(): bool
    {
        if ($this->getForm("image")) {
            return $this->getForm("image");
        }

        return false;
    }

    function getCampaignQueueName(): string
    {
        if (app()->environment('local')) {
            return (string) 'campaign-1';
        }
        return (string) 'campaign-' . ($this->id % 10);
    }

    function getAudioBookQueueName(): string
    {
        if (app()->environment('local')) {
            return (string) 'audio-1';
        }
        return (string) 'audio-' . ($this->id % 5);
    }

    public function imageCount(): int
    {
        if ($this->getForm("image")) {
            return $this->getForm("image_count") ?? 0;
        }

        return ImageSources::DEFAULT_IMAGE_COUNT->value ?? 5;
    }

    public function imageSources(): array
    {
        if ($this->getForm("image")) {
            return $this->getForm("image_sources");
        }

        return ImageSources::all();
    }

    /**
     * Get the average rating for this campaign.
     *
     * @return float|null
     */
    public function getAverageRating(): ?float
    {
        $reviewsCount = $this->reviews()->count();

        if ($reviewsCount === 0) {
            return null;
        }

        return round($this->reviews()->avg('rating'), 1);
    }

    /**
     * Get the rating summary (average and count) for this campaign.
     *
     * @return string|null
     */
    public function getRatingSummary(): ?string
    {
        $reviewsCount = $this->reviews()->count();

        if ($reviewsCount === 0) {
            return null;
        }

        $averageRating = $this->getAverageRating();

        return "{$averageRating} ({$reviewsCount})";
    }

    /**
     * Get star rating display for this campaign.
     *
     * @return string|null
     */
    public function getStarRating(): ?string
    {
        $averageRating = $this->getAverageRating();

        if ($averageRating === null) {
            return null;
        }

        // Round to nearest 0.5
        $roundedRating = round($averageRating * 2) / 2;

        // Create star display (e.g., "★★★★☆" for 4.0, "★★★★½" for 4.5)
        $fullStars = floor($roundedRating);
        $halfStar = ($roundedRating - $fullStars) >= 0.5;

        $stars = str_repeat('★', $fullStars);

        if ($halfStar) {
            $stars .= '½';
        }

        $emptyStars = 5 - $fullStars - ($halfStar ? 1 : 0);
        $stars .= str_repeat('☆', $emptyStars);

        return $stars;
    }

    /**
     * Get the review form configuration.
     *
     * @return array
     */
    public function getReviewFormConfig(): array
    {
        // Get the system-wide default configuration
        $defaultConfig = \App\Filament\Resources\CampaignResource\ReviewFormConfig::getDefaultConfig();

        // Get the stored configuration or use defaults
        $config = $this->review_form_config ?? [];

        // Merge with defaults to ensure all keys exist
        return array_merge($defaultConfig, $config);
    }

    /**
     * Update the review form configuration.
     *
     * @param array $config
     * @return void
     */
    public function updateReviewFormConfig(array $config): void
    {
        $this->review_form_config = $config;
        $this->save();
    }

    /**
     * Check if a specific field should be shown in the review form.
     *
     * @param string $field
     * @return bool
     */
    public function shouldShowReviewField(string $field): bool
    {
        $config = $this->getReviewFormConfig();
        return $config["show_{$field}_field"] ?? false;
    }

    /**
     * Check if a specific field is required in the review form.
     *
     * @param string $field
     * @return bool
     */
    public function isReviewFieldRequired(string $field): bool
    {
        $config = $this->getReviewFormConfig();
        return $config["{$field}_field_required"] ?? false;
    }

    public function requiredCredits()
    {
        $chapterCount = $this->chapters()->count();
        $sectionCount = $this->sections()->count();

        return
            ($chapterCount * CreditDeductEnum::EACH_CHAPTER_CREDIT->value) +
            ($sectionCount * CreditDeductEnum::EACH_SECTION_CREDIT->value) +
            CreditDeductEnum::EBOOK_GENERATION_CREDIT->value;
    }

    public function addErrorToMeta(string $errorKey, string $errorMessage): void
    {
        // Retrieve the current meta data, or initialize an empty array if it doesn't exist
        $meta = $this->meta ?? [];

        // Check if the 'errors' key exists in the meta data
        if (!isset($meta['errors'])) {
            // If 'errors' does not exist, create it
            $meta['errors'] = [];
        }

        // Add the new error to the 'errors' array
        $meta['errors'][$errorKey] = $errorMessage;

        // Save the updated meta data
        $this->meta = $meta;
        $this->save();
    }

}
