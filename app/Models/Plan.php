<?php

namespace App\Models;

use App\Enum\FeatureEnum;
use App\Enum\PlanTypeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use LemonSqueezy\Laravel\Subscription;

class Plan extends Model
{
    protected $casts = [
        'type' => PlanTypeEnum::class,
        'permissions' => 'json'
    ];

    protected $guarded=["id","created_at","updated_at"];

    // function subscriptions(): HasMany
    // {
    //     return $this->hasMany(Subscription::class);
    // }

    public function isEarlyBird(): bool
    {
        return $this->early_bird;
    }

    public function isTrialPlan()
    {
        return $this->slug == 'trial';
    }

    function users()
    {
        return $this->belongsToMany(User::class, 'subscriptions');
    }

    function hasEverything(): bool
    {
        return $this->permissions['has_everything'] ?? false;
    }

    function isRecurringSubscription(): bool
    {
        return in_array($this->type, PlanTypeEnum::getRecurringSubscriptionTypes());
    }

    function isRecurringLifetime(): bool
    {
        return in_array($this->type, PlanTypeEnum::getRecurringLTDTypes());
    }

    // public static function getDetailsFromSubscription(Subscription $subscription)
    // {
    //     return self::where('variant_id', $subscription->variant_id)
    //             ->where('product_id', $subscription->product_id)
    //             ->first();
    // }
    function hasPermissionResearchTools(): bool
    {
        return $this->hasEverything() || in_array(FeatureEnum::RESEARCH_TOOLS->value, $this->permissions['features'] ?? []);
    }
    function hasPermissionLinkSharing(): bool
    {
        return $this->hasEverything() || in_array(FeatureEnum::LINK_SHARING->value, $this->permissions['features'] ?? []);
    }
    function hasPermissionLeadCollection(): bool
    {
        return $this->hasEverything() || in_array(FeatureEnum::LEAD_COLLECTION->value, $this->permissions['features'] ?? []);
    }
    function hasPermissionAudioBookGeneration(): bool
    {
        return $this->hasEverything() || in_array(FeatureEnum::AUDIO_BOOK_GENERATION->value, $this->permissions['features'] ?? []);
    }
    function hasPermissionLuluIntegration(): bool
    {
        return $this->hasEverything() || in_array(FeatureEnum::LULU_INTEGRATION->value, $this->permissions['features'] ?? []);
    }
    function hasPermissionVideoToEbook(): bool
    {
        return $this->hasEverything() || in_array(FeatureEnum::VIDEO_TO_EBOOK->value, $this->permissions['features'] ?? []);
    }
    

    
}
