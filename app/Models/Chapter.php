<?php

namespace App\Models;

use App\Enum\CampaignStatusEnum;
use App\Jobs\OptimizeContentImagesJob;
use App\Traits\HasLogger;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Chapter extends Model
{
    use HasFactory, HasLogger;

    protected $casts = [
        'status' => CampaignStatusEnum::class,
    ];

    protected $guarded = ["id"];

    protected static function boot(): void
    {
        parent::boot();
        static::updated(function (Chapter $chapter) {
           if($chapter->isDirty('intro') && str_contains($chapter->intro, 'optimized=false')) {
               //run job optimizecontentimage
               OptimizeContentImagesJob::dispatch($chapter);
            }
        });
    }
    public function campaign(): BelongsTo
    {
        return $this->belongsTo(Campaign::class);
    }

    public function sections(): HasMany
    {
        return $this->hasMany(Section::class);
    }

    public function getPlainTitleAttribute(): string
    {
        return strip_tags($this->title);
    }

    function getChapterQueueName(): string
    {
        if (app()->environment('local')) {
            return (string) 'chapter-1';
        }
        return (string) 'chapter-'.($this->id % 10);
    }
}
