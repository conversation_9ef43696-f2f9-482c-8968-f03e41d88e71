<?php

namespace App\Models;

use App\Enum\CampaignStatusEnum;
use App\Traits\HasLogger;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class Section extends Model
{
    use HasFactory, HasLogger;

    protected $casts = [
        'status' => CampaignStatusEnum::class,
        'meta' => 'json'
    ];

    protected $guarded = ["id"];

    public function chapter(): BelongsTo
    {
        return $this->belongsTo(Chapter::class);
    }

    public function getPlainTitleAttribute(): string
    {
        return strip_tags($this->title);
    }

    function getSectionQueueName(): string
    {
        if (app()->environment('local')) {
            return (string) 'section-1';
        }
        return (string) 'section-'.($this->id % 10);
    }
    
    public function getMeta($key, $default = null)
    {
        return $this->meta[$key] ?? $default;
    }
}
