<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Pages;

use App\Enum\CampaignStatusEnum;
use App\Filament\Resources\ResearchEbookCampaignResource;
use Filament\Actions;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\CreateRecord;

class CreateResearchEbookCampaign extends CreateRecord
{
    protected static string $resource = ResearchEbookCampaignResource::class;

    protected function mutateFormDataBeforeCreate(array $data): array
    {
        $userId = auth()->user()->id;
        $data['user_id'] = $userId;
        $data['status'] = CampaignStatusEnum::PENDING->value;
        return $data;
    }

    public function mount(): void
    {
        if (!auth()->user()->getOpenAiApiKey()) {
            Notification::make()->title('You must set your OpenAI API key before you can create a campaign.')->danger()->send();
            redirect()->away('/settings');
            return;
        }

        parent::mount();
    }
}
