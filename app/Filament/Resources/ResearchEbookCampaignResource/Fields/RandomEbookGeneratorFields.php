<?php

namespace App\Filament\Resources\ResearchEbookCampaignResource\Fields;

use App\Enum\ResearchEbookCampaignIdeaTypeEnum;
use App\Service\AIModel\Contracts\AIClientInterface;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\HtmlString;

class RandomEbookGeneratorFields
{
    public static function make()
    {
        return Section::make('Select title')->schema([
            Radio::make('form.research_ebook_titles')
                 ->label("Chose your title to generate random ebook")
                 ->options(function (Get $get) {
                     $titleData = [];
                     if ($get("type") == ResearchEbookCampaignIdeaTypeEnum::RANDOM_EBOOK_GENERATOR->value){
                         $aiClient = app(AIClientInterface::class, ['aiModel' => $get("ai_model")]);
                         $apiKey = auth()->user()->getAiApiKey($get("ai_model"));

                         $prompt = promptBuilder(prompts()['random_ebook_title'], []);
                         $prompt .= prompts()['ignore_terms'];

                         $eBookTitles = $aiClient->callAIModel($prompt, $apiKey, $get("ai_model"));
                         $titleData = formatAIResponse($eBookTitles);

                         if (count($titleData) > 0) {
                             return collect($titleData["book_titles"])->mapWithKeys(function ($title) {
                                 return [$title => $title];
                             })->toArray();
                         }
                     }
                     return $titleData;
                })

        ]);
    }
}
