<?php

namespace App\Filament\Resources;

use App\Enum\UserRoleEnum;
use App\Filament\RelationManagers\SubscriptionRelationManager;
use App\Filament\Resources\UserResource\CreditRefundForm;
use App\Filament\Resources\UserResource\Pages;
use App\Filament\Resources\UserResource\RelationManagers\CampaignsRelationManager;
use App\Filament\Resources\UserResource\RelationManagers\CreditLogRelationManager;
use App\Filament\Resources\UserResource\RelationManagers\WebhookRelationManager;
use App\Models\User;
use App\Services\SubscriptionPlan;
use Carbon\Carbon;
use Filament\Forms;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\TextInput;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\TernaryFilter;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Hash;
use STS\FilamentImpersonate\Tables\Actions\Impersonate;
use Filament\Forms\Form;
use Filament\Tables\Table;
use Illuminate\Support\Facades\DB;
use LemonSqueezy\Laravel\Subscription;

class UserResource extends Resource
{
    protected static ?string $model = User::class;

    protected static ?string $recordTitleAttribute = 'email';

    protected static ?string $navigationIcon = 'heroicon-o-users';

    protected static ?string $navigationGroup = 'Admin';

    protected static ?int $navigationSort = 10;

    public static function shouldRegisterNavigation(): bool
    {
        return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin() || auth()->user()->isSupport();
    }

    public static function getEloquentQuery(): Builder
    {
        $query = parent::getEloquentQuery()->withCount(['campaigns', 'webhooks']);
        return $query;
    }

    public static function canCreate(): bool
    {
        return auth()->user()->isSuperAdmin() || auth()->user()->isAdmin();
    }

    public static function form(Form $form): Form
    {
        return $form
            ->columns(1)
            ->schema([
                Forms\Components\TextInput::make('name')
                    ->required()
                    ->maxLength(255),

                Forms\Components\TextInput::make('email')
                    ->email()
                    ->required()
                    ->unique(ignoreRecord: true)
                    ->maxLength(255),
                // Forms\Components\DateTimePicker::make('email_verified_at'),

                Forms\Components\Select::make('role')
                    ->default('user')
                    ->options(UserRoleEnum::getRoles())->required(),


                TextInput::make('password')
                    ->password()
                    ->dehydrateStateUsing(fn($state) => Hash::make($state))
                    ->dehydrated(fn($state) => filled($state))
                    ->required(fn(string $context): bool => $context === 'create'),

                // Forms\Components\Toggle::make('is_active')
                //     ->label('Active')
                //     ->default(true),




                // Forms\Components\DateTimePicker::make('two_factor_confirmed_at'),
                // Forms\Components\TextInput::make('current_team_id'),
                // Forms\Components\TextInput::make('profile_photo_path')->maxLength(2048),
            ]);
    }

    /**
     * @throws \Exception
     */
    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('name')->searchable(),
                Tables\Columns\TextColumn::make('email')->searchable(),
                Tables\Columns\TextColumn::make('role')->searchable()->alignCenter(),
                // Tables\Columns\ToggleColumn::make('early_bird')->label('Early Bird')->sortable()->alignCenter(),
                Tables\Columns\TextColumn::make('paa_entries_count')->label('Paa Entries')->sortable()->alignCenter()->toggleable()->toggledHiddenByDefault(),
                Tables\Columns\TextColumn::make('campaigns_count')->label('Campaigns')->sortable()->alignCenter()->toggleable(),
                Tables\Columns\TextColumn::make('plans_count')->label('Plans')->sortable()->alignCenter()->toggleable()->toggledHiddenByDefault(),
                Tables\Columns\ToggleColumn::make('is_active')->label('Active')->sortable()->alignCenter()->toggleable()->toggledHiddenByDefault(),
                Tables\Columns\TextColumn::make('created_at')->dateTime()->toggleable()->toggledHiddenByDefault(),
            ])
            ->filters([
                Tables\Filters\SelectFilter::make('role')->options(UserRoleEnum::getRoles()),

                TernaryFilter::make('is_active')
                    ->options([
                        'true' => 'Active',
                        'false' => 'Inactive',
                    ]),
            ])
            ->actions([
                Tables\Actions\ActionGroup::make([
                    Tables\Actions\Action::make('Logs')
                        ->icon('heroicon-o-clipboard-document-list')
                        ->url(fn(User $record): string => config('logging.channels.papertrail.query_log') . "User:" . $record->id)
                        ->openUrlInNewTab()
                        ->color('gray')
                        ->visible(fn(): bool => auth()->user()->isAdmin()),

                    Tables\Actions\EditAction::make()->visible(fn(): bool => auth()->user()->isAdmin()),

                    Tables\Actions\Action::make('User Activity')
                        ->icon('heroicon-o-clipboard-document-list')
                        ->url(fn(User $record): string => config('logging.channels.papertrail.query_log') . 'User:' . $record->id . ': User activity')
                        ->openUrlInNewTab()
                        ->color('gray')
                        ->visible(fn(): bool => auth()->user()->isAdmin()),

                    Tables\Actions\Action::make('Credit Refund')
                        ->label('Credit Refund')
                        ->color('primary')
                        ->form(function () {
                            return CreditRefundForm::getForm();
                        })
                        ->action(function (User $record, array $data) {
                            return CreditRefundForm::action($record, $data);
                        })
                        ->icon('heroicon-o-receipt-refund')
                        ->visible(fn(): bool => auth()->user()->isAdmin()),

                    Tables\Actions\DeleteAction::make()->visible(fn(): bool => auth()->user()->isAdmin()),

                    // Impersonate::make()->label('Impersonate'),
                ]),
            ])
            ->bulkActions([
                Tables\Actions\BulkAction::make('Assign Plan')
                    ->color('success')
                    ->form(function () {
                        return [
                            Forms\Components\Select::make('plan_id')->label('Plan')
                                ->options(\App\Models\Plan::all()->pluck('name', 'id')->toArray())
                                ->required(),
                        ];
                    })
                    ->action(function (Collection $records, array $data) {
                        $plan = \App\Models\Plan::find($data['plan_id']);
                        $records->each(function (User $user) use ($plan) {
                            $user->subscriptions()->create([
                                'plan_id' => $plan->id,
                                'payment_method' => 'free',
                                'status' => 'active',
                                'subscribed_at' => now(),
                                'subscription_updated_at' => now(),
                            ]);
                            $user->credits += $plan->credits;
                            $user->save();
                        });
                    })
                    ->icon('heroicon-o-tag')
                    ->deselectRecordsAfterCompletion(),

                Tables\Actions\DeleteBulkAction::make(),
            ])
            ->paginated(config('filament-config.pagination_option'));
    }

    public static function getRelations(): array
    {
        return [
            CampaignsRelationManager::class,
            SubscriptionRelationManager::class,
            WebhookRelationManager::class,
            CreditLogRelationManager::class
        ];
    }


    public static function getPages(): array
    {
        return [
            'index' => Pages\ListUsers::route('/'),
            'create' => Pages\CreateUser::route('/create'),
            'edit' => Pages\EditUser::route('/{record}/edit'),
        ];
    }

    public static function getGloballySearchableAttributes(): array
    {
        return ['name', 'email'];
    }

}
