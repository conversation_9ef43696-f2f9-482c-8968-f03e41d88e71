<?php

namespace App\Filament\Resources\SubscriptionResource\Pages;

use App\Filament\Resources\SubscriptionResource;
use App\Models\Subscription;
use App\Services\Logger;
use Filament\Resources\Pages\CreateRecord;

class CreateSubscription extends CreateRecord
{
    protected static string $resource = SubscriptionResource::class;

    public function create(bool $another = false): void
    {
        $subscription = Subscription::create($this->form->getState());

        if ($subscription->wasRecentlyCreated) {
            $subscription->user->log(
                'CreateSubscription: '.$subscription?->plan?->name.' subscription is assigned by '.auth()->user()->name.'\n'
                .'Your content limit for this subscription: '.$subscription?->plan?->content_quota
            );
        }
    }
}
