<?php

namespace App\Filament\Resources;

use App\Enum\FeatureEnum;
use App\Filament\Resources\CampaignResource\PublicShareForm;
use App\Filament\Resources\DownloadResource\Pages;
use App\Filament\Resources\DownloadResource\RelationManagers;
use App\Models\Download;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Storage;

class DownloadResource extends Resource
{
    protected static ?string $model = Download::class;

    protected static ?string $navigationIcon = 'heroicon-o-folder-arrow-down';

    protected static ?int $navigationSort = 4;

    public static function form(Form $form): Form
    {
        return $form
            ->schema([
                //
            ]);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                TextColumn::make('campaign.topic')->label("Campaign Topic")->url(function($record){
                    return "/campaign/{$record->campaign_id}";
                }),
                TextColumn::make('type')
                          ->label("Type")->badge()
                          ->formatStateUsing(fn ($state) => strtoupper($state)),
                TextColumn::make('user.name')
                          ->label("User name")
                          ->visible(function() {
                              return auth()->user()->isAdmin() || auth()->user()->isSuperAdmin();
                          }),

                Tables\Columns\TextColumn::make('created_at')
                                         ->label('Created')
                                         ->searchable()
                                         ->sortable()
                                         ->alignCenter()
                                         ->formatStateUsing(fn(Download $record) => $record->created_at->diffForHumans()),
            ])
            ->filters([
                //
            ])
            ->actions([
                //Tables\Actions\EditAction::make(),

                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\Action::make('Download file')
                                        ->label(fn (Download $record) => $record->type === 'mp3' ? 'Download Audio Book' : 'Download File')
                                         ->icon('heroicon-o-arrow-down-tray')
                                         ->action(function (Download $record) {
                                             if (!isS3FileExist($record->path)) {
                                                 Notification::make()
                                                             ->title("{$record->type} file not found.")->danger()->send();
                                             }
                                            if ($record->type == 'mp3') {
                                                return redirect()->to(Storage::disk('s3')->temporaryUrl($record->path, now()->addMinutes(30)));
                                            }
                                             return Storage::disk('s3')->download($record->path);
                                         })
                                         ->color('gray'),

                    Tables\Actions\Action::make('share_ebook')
                                         ->label("Share eBook")
                                         ->icon('heroicon-o-globe-americas')
                                         ->form(function (Download $record) {
                                             return PublicShareForm::getForm($record);
                                         })
                                         ->action(function (Download $record, array $data) {
                                             PublicShareForm::action($record, $data);
                                         })
                                         ->color('gray')
                                         ->visible(function (Download $record) {
                                             return $record->type == 'pdf' && isCampaignTypeEnabled( FeatureEnum::LINK_SHARING->value);
                                         })
                                         ->disabled(fn() => ! auth()->user()->getUser()->hasLinkSharing()),
                ])->iconButton(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            //
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListDownloads::route('/'),
            //'create' => Pages\CreateDownload::route('/create'),
            //'edit' => Pages\EditDownload::route('/{record}/edit'),
        ];
    }
    
    protected function getTemporaryUrl(Download $record): string
    {
        return Storage::disk('s3')->temporaryUrl($record->path,
            now()->addMinutes(30)
        );
    }
}
