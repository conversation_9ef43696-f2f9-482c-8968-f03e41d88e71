<?php

namespace App\Filament\Resources;

use App\Actions\HighestRequiredCreditCalculator;
use App\Actions\ReGenerateEbookAction;
use App\Actions\RunCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Enum\FeatureEnum;
use App\Filament\Resources\CampaignResource\AddErrorForm;
use App\Filament\Resources\CampaignResource\Forms\AudienceStyleWizardStep;
use App\Filament\Resources\CampaignResource\Forms\BasicInfoWizardStep;
use App\Filament\Resources\CampaignResource\Forms\EbookFormatWizardStep;
use App\Filament\Resources\CampaignResource\Forms\ConfigurePrintBookForm;
use App\Filament\Resources\CampaignResource\Pages;
use App\Filament\Resources\CampaignResource\PublicShareForm;
use App\Filament\Resources\CampaignResource\RelationManagers\DownloadsRelationManager;
use App\Filament\Resources\CampaignResource\ReviewFormConfig;
use App\Filament\Resources\CampaignResource\RelationManagers\ChaptersRelationManager;
use App\Filament\Resources\CampaignResource\RelationManagers\ReviewsRelationManager;
use App\Filament\Resources\CampaignResource\RelationManagers\SectionsRelationManager;
use App\Forms\Components\LivewireAudioPreview;
use App\Jobs\GenerateAudioBookJob;
use App\Jobs\GenerateCoverImage;
use App\Jobs\GenerateEbookJob;
use App\Models\Campaign;
use App\Models\EbookFormat;
use Exception;
use Filament\Forms;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\Resource;
use Filament\Tables;
use Filament\Tables\Filters\SelectFilter;
use Filament\Tables\Table;
use Illuminate\Support\Facades\Blade;
use Illuminate\Support\HtmlString;
use RyanChandler\FilamentProgressColumn\ProgressColumn;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Actions\Action as FormAction;
use Filament\Forms\Get;
use Illuminate\Support\Facades\Storage;
use Livewire\Livewire;

class CampaignResource extends Resource
{
    protected static ?string $model = Campaign::class;

    protected static ?string $navigationIcon = 'heroicon-o-queue-list';

    protected static ?int $navigationSort = 1;

    protected static ?string $slug = 'campaign';

    public static function form(Form $form): Form
    {
        $submitButtonLabel = "";

        return $form
            ->schema(function (Form $form) {
                $context = $form->getOperation();

                $wizard = Wizard::make([
                    BasicInfoWizardStep::make(),
                    AudienceStyleWizardStep::make(),
                    EbookFormatWizardStep::make(),
                ])->nextAction(function (Forms\Components\Actions\Action $action, $get, $context){
                    $pageLength = $get('page_length');

                    if (!$pageLength) return null;

                    $required = HighestRequiredCreditCalculator::getHighestRequiredCredits((int) $pageLength);
                    $remaining = auth()->user()->remainCredit();

                    if ($remaining < $required && $context == 'create') {
                        return $action->disabled(true);
                    }

                    return $action->disabled(false);
                });

                // Add submitAction only if not in 'view' context
                if ($context == 'create') {
                    $submitButtonLabel = "Save & Run Campaign";
                } elseif ($context == 'edit') {
                    $submitButtonLabel = "Save Campaign";
                }
                if ($context == 'create' || $context == 'edit') {
                    $wizard->submitAction(new HtmlString(Blade::render(<<<BLADE
                    <x-filament::button
                        type="submit"
                        size="sm"
                        form="create"
                    >
                        $submitButtonLabel
                    </x-filament::button>
                BLADE)));
                }

                return [
                    Section::make([
                        Forms\Components\Textarea::make('meta.error_message'),
                    ])->visible(fn($context, $record) => ($context == 'view') && $record->getMeta('error_message')),

                    $wizard,

                    Section::make([
                        Placeholder::make('status')
                            ->visibleOn('view')
                            ->content(fn(?Campaign $record): string => ucfirst($record?->status?->value) ?? '-'),

                        Placeholder::make('ai_model')
                                   ->visibleOn('view')
                                   ->content(fn(?Campaign $record): string => ucfirst($record?->ai_model) ?? '-'),

                        Placeholder::make('total_image')
                                   ->visibleOn('view')
                                   ->content(fn(?Campaign $record): string => $record?->getForm("image_count") ?? '-'),

                        Placeholder::make('required_words')
                            ->label("Required words")
                            ->visibleOn('view')
                            ->visible(function () {
                                return auth()->user()->isAdmin();
                            })
                            ->content(fn(?Campaign $record): string => $record->required_word_length ?? '-'),

                        Placeholder::make('total_words')
                            ->label("Total words")
                            ->visibleOn('view')
                            ->visible(function () {
                                return auth()->user()->isAdmin();
                            })
                            ->content(fn(?Campaign $record): string => $record->total_word_length ?? '-'),

                        Placeholder::make('created_at')
                            ->visibleOn('view')
                            ->label('Created Date')
                            ->content(fn(?Campaign $record): string => $record?->created_at?->diffForHumans() ?? '-'),
                    ])->columns(4)->visibleOn('view'),
                ];
            })->columns(1);
    }

    public static function table(Table $table): Table
    {
        return $table
            ->columns([
                Tables\Columns\TextColumn::make('topic')
                    ->searchable()->wrap()
                    ->limit(30)
                    ->sortable(),

                Tables\Columns\TextColumn::make('status')
                    ->badge()
                    ->sortable(),

                ProgressColumn::make('progress')
                    ->progress(function ($record) {
                        if ($record->status == CampaignStatusEnum::AUDIO_PROCESSING) {
                            // if(round((count($record->getMeta('chunks', [])) / ($record->getMeta("total_chunk") ?? 1)) * 100)>0){
                                return round((count($record->getMeta('chunks', [])) / ($record->getMeta("total_chunk") ?? 1)) * 100);
                            // }
                        }
                        $chapters = $record->chapters;
                        if ($record->status == CampaignStatusEnum::DONE) {
                            return 100;
                        }

                        if (
                            $record->status == CampaignStatusEnum::IMAGE_PROCESSING ||
                            $record->status == CampaignStatusEnum::IMAGE_PROCESSING_FAILED
                        ) {
                            return 90;
                        }

                        $totalChapters = $chapters->count();
                        $completedChapters = $chapters->where('status', CampaignStatusEnum::DONE)->count();

                        if ($totalChapters > 0) {
                            return round(($completedChapters / $totalChapters) * 100);
                        }

                        return 0;

                    })->width(220),

                Tables\Columns\TextColumn::make('user.name')
                    ->words(1, '')
                    ->searchable()
                    ->url(fn(Campaign $record): string => "/users/{$record->user_id}/edit")
                    ->sortable()
                    ->color('primary')
                    ->visible(auth()->user()->isAdmin() || auth()->user()->isSupport()),

                //                Tables\Columns\TextColumn::make("required_word_length")
//                    ->formatStateUsing(function ($record) {
//                        return $record->required_word_length . "-" . $record->total_word_length;
//                    })->label("Required & Total Words")
//                    ->visible(auth()->user()->isAdmin()),

                Tables\Columns\TextColumn::make('created_at')
                    ->label('Created')
                    ->searchable()
                    ->sortable()
                    ->alignCenter()
                    ->formatStateUsing(fn(Campaign $record) => $record->created_at->diffForHumans()),

            ])
            ->filters([
                SelectFilter::make('status')
                    ->options(CampaignStatusEnum::class)
                    ->label('Status')
                    ->placeholder('Select Status')
                    ->multiple(),
                // SelectFilter::make('form.image_generate_model')
                // ->options(ImageGenerateModelEnum::getLabels())
                // ->label('Model')
                // ->placeholder('Select Model')
                // ->multiple(),
            ])
            ->actions([
                //                Tables\Actions\Action::make('Download PDF')->label("Download PDF")
//                                     ->icon('heroicon-o-arrow-down-tray')
//                                     ->action(function ($record) {
//                                         $filePath = storage_path('app/' . $record->url);
//                                         if (!file_exists($filePath)) {
//                                             Notification::make()
//                                                         ->title('Ebook file not found.')->danger()->send();
//                                         }
//                                         return response()->download($filePath);
//                                     })
//                                     ->color('gray')->button()
//                                     ->visible(function ($record) {
//                                         $filePath = storage_path('app/' . $record->url);
//                                         return $record->url != null && file_exists($filePath);
//                                     }),

                Tables\Actions\Action::make('Re Generate eBook')->label("Re-generate Ebook")
                    ->icon('heroicon-o-document-text')
                    ->action(function ($record) {
                        return ReGenerateEbookAction::regenerateAction($record);
                    })
                    ->color('gray')->button()
                    ->visible(function ($record) {
                        return $record->status == CampaignStatusEnum::DONE &&
                            ($record->fileCount("pdf") < 1 || $record->fileCount("epub") < 1) && !($record->getMeta('chunks') && !$record->getMeta("audio"));
                    }),


                \Filament\Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\Action::make('Re-Generate eBook')->label("Re-generate Ebook")
                        ->icon('heroicon-o-document-text')
                        ->action(function ($record) {
                            return ReGenerateEbookAction::regenerateAction($record);
                        })
                        ->color('gray')
                        ->visible(function ($record) {
                            return $record->status == CampaignStatusEnum::DONE;
                        }),

                    Tables\Actions\Action::make('configure_review_form')
                        ->label('Configure Review Form')
                        ->icon('heroicon-o-star')
                        ->form(function (Campaign $record) {
                            return ReviewFormConfig::getForm($record);
                        })
                        ->action(function (Campaign $record, array $data, $action) {
                            // Check if the "Save as Default" checkbox is checked
                            $saveAsDefault = $data['save_as_default'] ?? false;

                            // Remove the checkbox value from the data before saving
                            unset($data['save_as_default']);

                            // Call the action method with the saveAsDefault flag
                            ReviewFormConfig::action($record, $data, $saveAsDefault);
                        })
                        ->color('success')
                        ->visible(function ($record) {
                            return $record->status == CampaignStatusEnum::DONE &&
                            isCampaignTypeEnabled( FeatureEnum::LEAD_COLLECTION->value);
                        })
                        ->disabled(fn() => ! auth()->user()->getUser()->hasLeadCollection()),


                    Tables\Actions\Action::make('configure_print_book')
                        ->label('Configure Print Book')
                        ->icon('heroicon-o-book-open')
                        ->form(function (Campaign $record) {
                            return ConfigurePrintBookForm::getForm($record);
                        })
                        ->action(function (Campaign $record, array $data) {
                            ConfigurePrintBookForm::action($record, $data);
                        })
                        ->color('info')
                        ->visible(function ($record) {
                            return $record->status == CampaignStatusEnum::DONE  && isCampaignTypeEnabled( FeatureEnum::LULU_INTEGRATION->value);
                        })
                        ->disabled(fn() => ! auth()->user()->getUser()->hasLuluIntegration()),


//                    Tables\Actions\Action::make('Download PDF')->label("Download PDF")
//                        ->icon('heroicon-o-arrow-down-tray')
//                        ->action(function ($record) {
//                            if (!isS3FileExist($record->getPdf())) {
//                                Notification::make()
//                                    ->title('Ebook pdf file not found.')->danger()->send();
//                            }
//                            return Storage::disk('s3')->download($record->getPdf());
//                        })
//                        ->color('gray')
//                        ->visible(function ($record) {
//                            return $record->getPdf() != null && isS3FileExist($record->getPdf());
//                        }),


//                    Tables\Actions\Action::make('Download Epub')->label("Download Epub")
//                        ->icon('heroicon-o-arrow-down-tray')
//                        ->action(function ($record) {
//                            if (!isS3FileExist($record->getEpub())) {
//                                Notification::make()
//                                    ->title('Ebook epub file not found.')->danger()->send();
//                            }
//                            return Storage::disk('s3')->download($record->getEpub());
//                        })
//                        ->color('gray')
//                        ->visible(function ($record) {
//                            return $record->getPdf() != null && isS3FileExist($record->getPdf());
//                        }),

//                    Tables\Actions\Action::make('share_ebook')
//                                        ->label("Share eBook")
//                                        ->icon('heroicon-o-globe-americas')
//                                        ->form(function (Campaign $record) {
//                                            return PublicShareForm::getForm($record);
//                                        })
//                                        ->action(function (Campaign $record, array $data) {
//                                            PublicShareForm::action($record, $data);
//                                        })
//                                         ->color('gray')
//                                         ->visible(function ($record) {
//                                            return $record->getPdf() != null && isS3FileExist($record->getPdf());
//                                         }),

                    Tables\Actions\Action::make('Generate Audio Book')
                        ->label(fn($record) =>  $record->status ==CampaignStatusEnum::AUDIO_PROCESSING_FAILED ? "Regenerate Audio Book" : "Generate Audio Book")
                        ->icon('heroicon-o-speaker-wave')
                        ->form(fn($record) =>
                        $record->status ==CampaignStatusEnum::AUDIO_PROCESSING_FAILED && $record->getMeta("voice_model") ?
                        []
                        :
                        [
                            Select::make('voice_model')
                                ->label('Select Voice Model')
                                ->options([
                                    'alloy' => 'Alloy',
                                    'ash' => 'Ash',
                                    'coral' => 'Coral',
                                    'echo' => 'Echo',
                                    'fable' => 'Fable',
                                    'onyx' => 'Onyx',
                                    'nova' => 'Nova',
                                    'sage' => 'Sage',
                                    'shimmer' => 'Shimmer',
                                ])
                                ->helperText(new HtmlString("<span style='color:red;'>100 Credits will be deducted for each audio generation. </span>"))
                                ->default('alloy')
                                ->reactive(),
                            ViewField::make('audion_player')->view('components.audio-player')
                                    ->statePath('voice_model')
                                    ->reactive(),
                        ]
                        
                        )
                        ->action(function (Campaign $campaign, array $data) {
                        if($campaign->status == CampaignStatusEnum::AUDIO_PROCESSING_FAILED && $campaign->getMeta("voice_model")){
                            $data['voice_model'] = $campaign->getMeta("voice_model");
                        }
                            $campaign->saveMeta("voice_model", $data['voice_model']);
                            GenerateAudioBookJob::dispatch($campaign)->onQueue($campaign->getAudioBookQueueName());

                            Notification::make()
                                        ->title("We're processing your audiobook with {$data['voice_model']}. Please wait.")
                                        ->success()
                                        ->send();
                        })
                        ->color('gray')
                        ->visible(fn($record) => in_array($record->status, [CampaignStatusEnum::DONE,CampaignStatusEnum::AUDIO_PROCESSING_FAILED])
                                  && isCampaignTypeEnabled( FeatureEnum::AUDIO_BOOK_GENERATION->value))
                        ->disabled(fn() => ! auth()->user()->getUser()->hasAudioBookGeneration() || !auth()->user()->getUser()->getOpenAiApiKey()),



                    // Tables\Actions\Action::make('Download Audio Book')
                    //     ->label("Download Audio Book")
                    //     ->icon('heroicon-o-arrow-down-tray')
                    //     ->url(fn($record) => Storage::disk('s3')->temporaryUrl(
                    //         'audio/' . basename($record->getMeta("audio")),
                    //         now()->addMinutes(30) // Generates a signed URL valid for 30 minutes
                    //     )) // Assuming 'audio' meta contains the file URL
                    //     ->openUrlInNewTab()
                    //     ->color('success')
                    //     ->visible(fn($record) => $record->getMeta("audio")),

                    Tables\Actions\Action::make('Logs')
                        ->icon('heroicon-o-clipboard-document-list')
                        ->url(fn(Campaign $record): string => config('logging.channels.papertrail.query_log') . 'Campaign:' . $record->id . ':')
                        ->openUrlInNewTab()
                        ->color('gray')
                        ->visible(fn(): bool => auth()->user()->isAdmin()),

                    Tables\Actions\Action::make('Retry')
                        ->label("Re-try")
                        ->action(function (Campaign $record, RunCampaignAction $runCampaign) {
                            $record->log("Campaign retry starting for campaign: {$record->id}");
                            $runCampaign->execute($record, true);
                            return Notification::make()->success()->title('Campaign retrying')->send();
                        })->icon('heroicon-m-play')->visible(function (Campaign $record) {
                            return ($record->status != CampaignStatusEnum::DONE &&
                                    $record->status != CampaignStatusEnum::IN_PROGRESS &&
                                    $record->status != CampaignStatusEnum::DRAFT
                            );
                        }),

                    \Filament\Tables\Actions\ReplicateAction::make()->label('Clone')
                        ->beforeReplicaSaved(function (Campaign $replica, array $data) {
                            $replica->status = CampaignStatusEnum::DRAFT->value;
                            $replica->total_word_length = null;
                            $replica->required_word_length = null;
                            $replica->title = null;
                            $replica->url = null;
                            $replica->meta = null;
                            $replica->cover_image = null;
                            $replica->user_id = auth()->user()->getUser()->id;

                            $ebookFormat = EbookFormat::find($replica->ebook_format_id);
                            $newEbookFormat = EbookFormat::create($ebookFormat->toArray());
                            $replica->ebook_format_id = $newEbookFormat->id;
                        })->form([
                                Wizard::make([
                                    BasicInfoWizardStep::make(),
                                    AudienceStyleWizardStep::make(),
                                ]),
                            ]),

                    Tables\Actions\Action::make('Start')->action(function (Campaign $record, RunCampaignAction $runCampaign) {
                        $runCampaign->execute($record, true);
                        return Notification::make()->success()->title('Campaign starting!')->send();
                    })->icon('heroicon-m-play')->visible(function (Campaign $record) {
                        return $record->status == CampaignStatusEnum::DRAFT;
                    }),

                    Tables\Actions\Action::make('Add Error Message')
                        ->label('Add Error Message')
                        ->color('danger')
                        ->form(function () {
                            return AddErrorForm::getForm();
                        })
                        ->action(function (Campaign $record, array $data) {
                            return AddErrorForm::action($record, $data);
                        })
                        ->icon('heroicon-o-exclamation-triangle')
                        ->visible(fn(): bool => auth()->user()->isAdmin()),

                    Tables\Actions\ViewAction::make()
                        ->visible(fn(Campaign $record): bool => $record->status->value == CampaignStatusEnum::DONE->value),

                    Tables\Actions\EditAction::make()
                        ->visible(fn(Campaign $record): bool => $record->status->value == CampaignStatusEnum::DONE->value),

                    Tables\Actions\DeleteAction::make()
                        ->visible(fn(Campaign $record): bool => $record->status->value == CampaignStatusEnum::DONE->value),
                ])->iconButton(),
            ])
            ->modifyQueryUsing(function ($query) {
                $user = auth()->user();
                if ($user->isAdmin() || $user->isSuperAdmin() || $user->isSupport()) {
                    return $query;
                }
                return $query->where('user_id', $user->getUser()->id);
            })
            ->defaultSort('campaigns.id', 'desc')
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    // Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }

    public static function getRelations(): array
    {
        return [
            ChaptersRelationManager::class,
            SectionsRelationManager::class,
            ReviewsRelationManager::class,
            DownloadsRelationManager::class,
        ];
    }

    public static function getPages(): array
    {
        return [
            'index' => Pages\ListCampaigns::route('/'),
            'create' => Pages\CreateCampaign::route('/create'),
            'view' => Pages\ViewCampaign::route('/{record}'),
            'edit' => Pages\EditCampaign::route('/{record}/edit'),
        ];
    }
}
