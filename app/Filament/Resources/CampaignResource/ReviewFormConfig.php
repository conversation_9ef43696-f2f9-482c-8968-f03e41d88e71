<?php

namespace App\Filament\Resources\CampaignResource;

use App\Models\Campaign;
use App\Models\ReviewSetting;
use Filament\Forms\Components\Grid;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\Toggle;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Checkbox;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Actions;
use Filament\Forms\Get;

class ReviewFormConfig
{
    /**
     * The key used to store default review form configuration in system settings.
     */
    public const DEFAULT_CONFIG_KEY = 'default_review_form_config';

    /**
     * Get the default review form configuration.
     *
     * @return array
     */
    public static function getDefaultConfig(): array
    {
        // Default configuration if nothing is saved
        $defaultConfig = [
            'title' => 'We\'d Love Your Feedback!',
            'subtitle' => 'Please take a moment to rate your experience with this eBook.',
            'show_name_field' => true,
            'show_email_field' => true,
            'show_rating_field' => true,
            'show_comment_field' => true,
            'name_field_required' => true,
            'email_field_required' => true,
            'rating_field_required' => true,
            'comment_field_required' => false,
            'thank_you_message' => 'Thank you for your feedback!',
            'submit_button_text' => 'Submit Review',
            'close_button_text' => 'Close',
            'show_after_seconds' => 30,
        ];

        // Get saved default configuration from system settings
        $savedConfig = ReviewSetting::get(self::DEFAULT_CONFIG_KEY, []);

        // Merge saved configuration with default configuration
        return array_merge($defaultConfig, $savedConfig);
    }

    /**
     * Save the current configuration as the default.
     *
     * @param array $config
     * @return void
     */
    public static function saveAsDefault(array $config): void
    {
        // Clean up any nested arrays that might come from form data
        if (isset($config['reviewFormData'])) {
            $config = $config['reviewFormData'];
        }

        ReviewSetting::set(
            self::DEFAULT_CONFIG_KEY,
            $config,
            'review_form',
            'Default configuration for eBook review forms'
        );
    }

    /**
     * Get the form schema for review form configuration.
     *
     * @param Campaign|null $record
     * @return array
     */
    public static function getForm(?Campaign $record = null): array
    {
        // Get default config
        $defaultConfig = self::getDefaultConfig();

        // Get current config if available, otherwise use default
        $config = $record ? $record->getReviewFormConfig() : $defaultConfig;



        return [
            Section::make('Review Form Settings')
                ->description('Customize how the review form appears to readers')
                ->schema([
                    Grid::make(3)
                        ->schema([
                            TextInput::make('title')
                                ->label('Form Title')
                                ->default($config['title'])
                                ->required(),

                            TextInput::make('show_after_seconds')
                                ->label('Show After (seconds)')
                                ->numeric()
                                ->default($config['show_after_seconds'])
                                ->required(),

                            Textarea::make('subtitle')
                                ->label('Form Subtitle')
                                ->default($config['subtitle'])
                                ->rows(2)
                                ->columnSpan(3),
                        ]),

                    Section::make('Form Fields')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Toggle::make('show_name_field')
                                        ->label('Show Name Field')
                                        ->default($config['show_name_field'])
                                        ->reactive(),

                                    Toggle::make('name_field_required')
                                        ->label('Name Field Required')
                                        ->default($config['name_field_required'])
                                        ->visible(fn ($get) => $get('show_name_field')),

                                    Toggle::make('show_email_field')
                                        ->label('Show Email Field')
                                        ->default($config['show_email_field'])
                                        ->reactive(),

                                    Toggle::make('email_field_required')
                                        ->label('Email Field Required')
                                        ->default($config['email_field_required'])
                                        ->visible(fn ($get) => $get('show_email_field')),

                                    Toggle::make('show_rating_field')
                                        ->label('Show Rating Field')
                                        ->default($config['show_rating_field'])
                                        ->reactive(),

                                    Toggle::make('rating_field_required')
                                        ->label('Rating Field Required')
                                        ->default($config['rating_field_required'])
                                        ->visible(fn ($get) => $get('show_rating_field')),

                                    Toggle::make('show_comment_field')
                                        ->label('Show Comment Field')
                                        ->default($config['show_comment_field'])
                                        ->reactive(),

                                    Toggle::make('comment_field_required')
                                        ->label('Comment Field Required')
                                        ->default($config['comment_field_required'])
                                        ->visible(fn ($get) => $get('show_comment_field')),
                                ]),
                        ]),

                    Section::make('Button & Messages')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    TextInput::make('submit_button_text')
                                        ->label('Submit Button Text')
                                        ->default($config['submit_button_text'])
                                        ->required(),

                                    TextInput::make('close_button_text')
                                        ->label('Close Button Text')
                                        ->default($config['close_button_text'])
                                        ->required(),

                                    Textarea::make('thank_you_message')
                                        ->label('Thank You Message')
                                        ->default($config['thank_you_message'])
                                        ->rows(2)
                                        ->columnSpan(2),
                                ]),
                        ]),

                    Actions::make([
                           Action::make('preview')
                                 ->label('Preview Format')
                                 ->icon('heroicon-o-eye')
                                 ->button()
                                 ->color('secondary')
                                 ->extraAttributes(['class' => 'preview-button'])
                                 ->action(function (Get $get, $livewire) {
                                     $formatData = [
                                         'title'             => $get('title'),
                                         'subtitle'          => $get('subtitle'),
                                         'show_after_seconds' => $get('show_after_seconds'),
                                         'show_name_field'   => $get('show_name_field'),
                                         'show_email_field'  => $get('show_email_field'),
                                         'show_rating_field' => $get('show_rating_field'),
                                         'show_comment_field' => $get('show_comment_field'),
                                         'name_field_required' => $get('name_field_required'),
                                         'email_field_required' => $get('email_field_required'),
                                         'rating_field_required' => $get('rating_field_required'),
                                         'comment_field_required' => $get('comment_field_required'),
                                         'submit_button_text' => $get('submit_button_text'),
                                         'close_button_text' => $get('close_button_text'),
                                         'thank_you_message' => $get('thank_you_message'),

                                     ];
                                     $livewire->dispatch('preview-review', ['formatData' => $formatData]);
                                 }),
                       ]),
                ]),
        ];
    }

    /**
     * Handle the action when the form is submitted.
     *
     * @param Campaign $record
     * @param array $data
     * @param bool $saveAsDefault
     * @return void
     */
    public static function action(Campaign $record, array $data, bool $saveAsDefault = false): void
    {
        // Update the campaign's review form configuration
        $record->updateReviewFormConfig($data);

        // Save as default if requested
        if ($saveAsDefault) {
            self::saveAsDefault($data);

            // Show a success notification for saving as default
            Notification::make()
                ->title('Review form settings updated and saved as default template')
                ->success()
                ->send();
        } else {
            // Show a regular success notification
            Notification::make()
                ->title('Review form settings updated successfully')
                ->success()
                ->send();
        }
    }
}
