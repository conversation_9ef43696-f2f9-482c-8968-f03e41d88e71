<?php

namespace App\Filament\Resources\CampaignResource\Pages;

use App\Actions\ReGenerateEbookAction;
use App\Actions\RunCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Enum\CampaignTypeEnum;
use App\Enum\CreditDeductEnum;
use App\Enum\FeatureEnum;
use App\Filament\Resources\CampaignResource;
use App\Filament\Resources\CampaignResource\AddErrorForm;
use App\Filament\Resources\CampaignResource\Forms\AudienceStyleWizardStep;
use App\Filament\Resources\CampaignResource\Forms\BasicInfoWizardStep;
use App\Filament\Resources\CampaignResource\Forms\ConfigureAudioBookForm;
use App\Filament\Pages\Addons;
use App\Jobs\GenerateCoverImage;
use App\Jobs\GenerateEbookJob;
use App\Models\Campaign;
use App\Models\EbookFormat;
use Closure;
use Exception;
use Filament\Actions\Action;
use Filament\Actions\ActionGroup;
use Filament\Actions\DeleteAction;
use Filament\Actions\EditAction;
use Filament\Actions\ReplicateAction;
use Filament\Forms;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Form;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\ViewRecord;
use App\Enum\SiteFeaturesEnum;
use App\Filament\Resources\CampaignResource\PublicShareForm;
use Illuminate\Support\Facades\Storage;
use Filament\Infolists\Components;
use Filament\Infolists\Infolist;
use Illuminate\Support\HtmlString;

class ViewCampaign extends ViewRecord
{
    protected static string $resource = CampaignResource::class;

    protected function getHeaderActions(): array
    {
        return [
            // \Filament\Tables\Actions\BulkActionGroup::make([
            //     Action::make('Download Audio Book')
            //           ->label("Download Audio Book")
            //           ->icon('heroicon-o-arrow-down-tray')
            //           ->url(fn($record) => Storage::disk('s3')->temporaryUrl(
            //               'audio/' . basename($record->getMeta("audio")),
            //               now()->addMinutes(30)
            //           ))
            //           ->openUrlInNewTab()
            //           ->color('success')
            //           ->visible(fn($record) => $record->getMeta("audio")),
            // ])->button()->label("Export")->color("success"),

            \Filament\Tables\Actions\BulkActionGroup::make([
                Action::make('Generate Audio Book')
                     ->label("Generate Audio Book")
                     ->icon('heroicon-o-speaker-wave')
                     ->form(fn(Campaign $campaign) => ConfigureAudioBookForm::getForm($campaign))
                     ->action(fn(Campaign $campaign, array $data) => ConfigureAudioBookForm::action($campaign, $data))
                     ->color('gray')
                     ->visible(ConfigureAudioBookForm::getVisibility()),

                Action::make('Generate eBook')->label("Re-generate Ebook")
                      ->icon('heroicon-o-document-text')
                      ->action(function ($record) {
                          return ReGenerateEbookAction::regenerateAction($record);

                      })
                      ->color('gray')
                      ->visible(fn($record) => $record->status == CampaignStatusEnum::DONE),

                Action::make('Logs')
                      ->icon('heroicon-o-clipboard-document-list')
                      ->url(fn(Campaign $record): string => config('logging.channels.papertrail.query_log') . 'Campaign:' . $record->id . ':')
                      ->openUrlInNewTab()
                      ->color('gray')
                      ->visible(fn(): bool => auth()->user()->isAdmin()),

                Action::make('Retry')
                      ->label("Re-try")
                      ->action(function (Campaign $record, RunCampaignAction $runCampaign) {
                          $record->log("Campaign retry starting for campaign: {$record->id}");
                          $runCampaign->execute($record, true);
                          return Notification::make()->success()->title('Campaign retrying')->send();
                      })->icon('heroicon-m-play')->visible(function (Campaign $record) {
                        return ($record->status != CampaignStatusEnum::DONE  &&
                                $record->status != CampaignStatusEnum::DRAFT
                        );
                    }),
                Action::make('Add Error Message')
                    ->label('Add Error Message')
                    ->color('danger')
                    ->form(function () {
                        return AddErrorForm::getForm();
                    })
                    ->action(function (Campaign $record, array $data) {
                        return AddErrorForm::action($record, $data);
                    })
                    ->icon('heroicon-o-exclamation-triangle')
                    ->visible(fn(): bool => auth()->user()->isAdmin()),

                ReplicateAction::make()->label('Clone')
                    ->beforeReplicaSaved(function (Campaign $replica, array $data) {
                        $replica->status = CampaignStatusEnum::DRAFT->value;
                        $replica->total_word_length = null;
                        $replica->required_word_length = null;
                        $replica->title = null;
                        $replica->url = null;
                        $replica->meta = null;
                        $replica->cover_image = null;

                        $ebookFormat = EbookFormat::find($replica->ebook_format_id);
                        $newEbookFormat = EbookFormat::create($ebookFormat->toArray());
                        $replica->ebook_format_id = $newEbookFormat->id;
                    })->form([
                        Wizard::make([
                            BasicInfoWizardStep::make(),
                            AudienceStyleWizardStep::make(),
                        ]),
                    ]),

                Action::make('Start')->action(function (Campaign $record, RunCampaignAction $runCampaign) {
                    $runCampaign->execute($record, true);
                    return Notification::make()->success()->title('Campaign starting!')->send();
                })->icon('heroicon-m-play')->visible(function (Campaign $record) {
                    return $record->status == CampaignStatusEnum::DRAFT;
                }),

            ])->button()->label("Action")->color("warning"),

            EditAction::make()->icon("heroicon-m-pencil"),
        ];
    }

    public function infolist(Infolist $infolist): Infolist
    {
        return $infolist
            ->schema([
                Components\Section::make(' Campaign Error')
                    ->schema([
                        Components\TextEntry::make('meta.error_message')
                        ->label('')
                        ->color('danger')
                        ->visible(function ($record){
                            return $record->getMeta("error_message");
                        })
                    ])->visible(function ($record){
                            return $record->getMeta("error_message");
                        }),
                Components\Section::make(' Campaign information')
                    ->schema([
                        Components\Split::make([
                            Components\Grid::make(3)
                                ->schema([
                                    Components\Group::make([
                                        Components\TextEntry::make('topic')->label('Campaign topic'),
                                        Components\TextEntry::make('author'),
                                        Components\TextEntry::make('status')
                                            ->badge()
                                    ]),
                                    Components\Group::make([
                                        Components\TextEntry::make('type')
                                            ->formatStateUsing(fn (string $state): string =>
                                                CampaignTypeEnum::getAllowedTypes()[$state] ?? 'Unknown'
                                            ),
                                        Components\TextEntry::make('ai_model'),
                                        Components\TextEntry::make('page_length')
                                            ->formatStateUsing(fn (string $state): string => match ($state) {
                                                "30" => "20-40 pages",
                                                "50" => "40-60 pages",
                                                "70" => "60-80 pages",
                                                "90" => "80-100 pages",
                                                "110" => "100-120 pages",
                                                "130" => "120-140 pages",
                                                "150" => "140-160 pages",
                                                "170" => "160-180 pages",
                                            }),
                                    ]),
                                    Components\Group::make([
                                        Components\TextEntry::make('form.language')->label('Language'),
                                        Components\TextEntry::make('created_at')->date(),
                                    ]),
                                ]),
                        ])->from('lg'),
                        Components\Split::make([
                            Components\Grid::make(1)
                                ->schema([
                                    Components\Group::make([
                                        Components\TextEntry::make('title')->label('Ebook Title')
                                            ->formatStateUsing(fn (string $state): string =>
                                                strip_tags($state)
                                            ),
                                        Components\TextEntry::make('form.context')->label('Context')
                                            ->formatStateUsing(fn (string $state): string =>
                                                strip_tags($state)
                                            ),
                                    ]),
                                ]),
                        ])->from('lg'),

                       Components\KeyValueEntry::make('meta.errors')
                        ->label('Errors')
                        ->visible(function ($record){
                            return $record->getMeta("errors");
                        })
                        ->hidden(auth()->user()->getUser()->isUser()),
                    ])
                    ->collapsible(),
            ]);
    }
}
