<?php

namespace App\Filament\Resources\CampaignResource\Pages;

use App\Actions\ReGenerateEbookAction;
use App\Actions\RunCampaignAction;
use App\Enum\CampaignStatusEnum;
use App\Enum\CreditDeductEnum;
use App\Enum\FeatureEnum;
use App\Filament\Resources\CampaignResource;
use App\Filament\Resources\CampaignResource\AddErrorForm;
use App\Filament\Resources\CampaignResource\Forms\AudienceStyleWizardStep;
use App\Filament\Resources\CampaignResource\Forms\BasicInfoWizardStep;
use App\Filament\Resources\CampaignResource\PublicShareForm;
use App\Jobs\GenerateAudioBookJob;
use App\Jobs\GenerateCoverImage;
use App\Jobs\GenerateEbookJob;
use App\Models\Campaign;
use App\Models\EbookFormat;
use Filament\Actions;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Components\Wizard;
use Filament\Notifications\Notification;
use Filament\Resources\Pages\EditRecord;
use Illuminate\Support\Facades\Storage;
use App\Models\BackgroundImage;
use App\Models\Template;
use Illuminate\Support\HtmlString;


class EditCampaign extends EditRecord
{
    protected static string $resource = CampaignResource::class;

    public function setDesign($id): void
    {
        $template = Template::find($id);

        $currentData = $this->form->getRawState();
        $currentData['ebookFormat']['font'] = $template["font"];
        $currentData['ebookFormat']['font_size'] = $template["font_size"];
        $currentData['ebookFormat']['heading'] = $template["heading"];
        $currentData['ebookFormat']['sub_heading'] = $template["sub_heading"];
        $currentData['ebookFormat']['line_space'] = $template["line_space"];
        $currentData['ebookFormat']['paragraph_space'] = $template["paragraph_space"];
        $currentData['ebookFormat']['page_size'] = $template["page_size"];
        $currentData['ebookFormat']['margins'] = $template["margins"];
        $currentData['ebookFormat']['text_align'] = $template["text_align"];
        $currentData['ebookFormat']['background_image'] = $template->image ?? "";
        // $currentData['ebookFormat']['background_opacity'] = $template["background_opacity"] ?? 1;
        $currentData['ebookFormat']['margin_top'] = $template["margin_top"] ?? "";
        $currentData['ebookFormat']['margin_bottom'] = $template["margin_bottom"] ?? "";
        $currentData['ebookFormat']['margin_left'] = $template["margin_left"] ?? "";
        $currentData['ebookFormat']['margin_right'] = $template["margin_right"] ?? "";
        $this->form->fill($currentData);

    }
    // Method to set the background image with opacity
    public function setBackgroundImage($src, $opacity = 1.0): void
    {
        $currentData = $this->form->getRawState();
        $currentData['background_image'] = $src==""?"none":$src;
        $backgroundImage = BackgroundImage::where('src', $src)->first();
        $currentData['background_opacity'] = $backgroundImage->opacity ?? 1.0;

        $this->form->fill($currentData);

    }
    protected function afterSave(): void
    {
        // $campaign = $this->getRecord();
        // if ($campaign->wasChanged(['url', 'form', "limit", "limit_per_day"])) {
        //     $campaign->updatwMWCampaign();
        // }
    }

    protected function getHeaderActions(): array
    {
        return [
            // Actions\DeleteAction::make(),

            //            Action::make('Download PDF')->label("Download PDF")
//                  ->icon('heroicon-o-arrow-down-tray')
//                  ->action(function ($record) {
//                      $filePath = storage_path('app/' . $record->url);
//                      if (!file_exists($filePath)) {
//                          Notification::make()
//                                      ->title('Ebook file not found.')->danger()->send();
//                      }
//                      return response()->download($filePath);
//                  })
//                  ->color('gray')->visible(function ($record) {
//                    return $record->url != null;
//                })->button()->color("success"),

            \Filament\Tables\Actions\BulkActionGroup::make([
//                Action::make('Download PDF')->label("Download PDF")
//                    ->icon('heroicon-o-arrow-down-tray')
//                    ->action(function ($record) {
//                        if (!isS3FileExist($record->getPdf())) {
//                            Notification::make()
//                                        ->title('Ebook pdf file not found.')->danger()->send();
//                        }
//                        return Storage::disk('s3')->download($record->getPdf());
//                    })
//                    ->color('gray')
//                    ->visible(function ($record) {
//                        return $record->getPdf() != null && isS3FileExist($record->getPdf());
//                    }),

//                Action::make('Download Epub')->label("Download Epub")
//                    ->icon('heroicon-o-arrow-down-tray')
//                    ->action(function ($record) {
//                        if (!isS3FileExist($record->getEpub())) {
//                            Notification::make()
//                                        ->title('Ebook epub file not found.')->danger()->send();
//                        }
//                        return Storage::disk('s3')->download($record->getEpub());
//                    })
//                    ->color('gray')
//                    ->visible(function ($record) {
//                        return $record->getPdf() != null && isS3FileExist($record->getPdf());
//                    }),
//
//                    Action::make('share_ebook')
//                            ->label("Share eBook")
//                            ->icon('heroicon-o-globe-americas')
//                            ->form(function (Campaign $record) {
//                                return PublicShareForm::getForm($record);
//                            })
//                            ->action(function (Campaign $record, array $data) {
//                                PublicShareForm::action($record, $data);
//                            })
//                            ->color('gray')
//                            ->visible(function ($record) {
//                                return $record->getPdf() != null && isS3FileExist($record->getPdf());
//                            }),

                Action::make('Download Audio Book')
                    ->label("Download Audio Book")
                    ->icon('heroicon-o-arrow-down-tray')
                    ->url(fn($record) => Storage::disk('s3')->temporaryUrl(
                        'audio/' . basename($record->getMeta("audio")),
                        now()->addMinutes(30) // Generates a signed URL valid for 30 minutes
                    )) // Assuming 'audio' meta contains the file URL
                    ->openUrlInNewTab()
                    ->color('success')
                    ->visible(fn($record) => $record->getMeta("audio")),


            ])->button()->label("Export")->color("success"),

            \Filament\Tables\Actions\BulkActionGroup::make([
                Action::make('Generate Audio Book')
                      ->label("Generate Audio Book")
                      ->icon('heroicon-o-speaker-wave')
                      ->form([
                          Select::make('voice_model')
                                ->label('Select Voice Model')
                                ->options([
                                    'alloy' => 'Alloy',
                                    'ash' => 'Ash',
                                    'coral' => 'Coral',
                                    'echo' => 'Echo',
                                    'fable' => 'Fable',
                                    'onyx' => 'Onyx',
                                    'nova' => 'Nova',
                                    'sage' => 'Sage',
                                    'shimmer' => 'Shimmer',
                                ])
                                ->helperText(new HtmlString("<span style='color:red;'>100 Credits will be deducted for each audio generation. </span>"))
                                ->default('alloy')
                                ->reactive(),
                          ViewField::make('audion_player')->view('components.audio-player')
                                   ->statePath('voice_model')
                                   ->reactive(),
                      ])
                      ->action(function (Campaign $campaign, array $data) {
                          $campaign->saveMeta("voice_model", $data['voice_model']);
                          GenerateAudioBookJob::dispatch($campaign)->onQueue($campaign->getAudioBookQueueName());

                          Notification::make()
                                      ->title("We're processing your audiobook with {$data['voice_model']}. Please wait.")
                                      ->success()
                                      ->send();
                      })
                      ->color('gray')
                      ->visible(fn($record) => $record->status == CampaignStatusEnum::DONE && !$record->getMeta("audio") && !$record->getMeta("voice_model") && isCampaignTypeEnabled( FeatureEnum::AUDIO_BOOK_GENERATION->value)  )
                      ->disabled(fn() => ! auth()->user()->getUser()->hasAudioBookGeneration()),


                Action::make('Generate eBook')->label("Re-generate Ebook")
                    ->icon('heroicon-o-document-text')
                    ->action(function ($record) {

                        return ReGenerateEbookAction::regenerateAction($record);
                    })
                    ->color('gray')
                    ->visible(fn($record) => $record->status == CampaignStatusEnum::DONE),

                Action::make('Logs')
                    ->icon('heroicon-o-clipboard-document-list')
                    ->url(fn(Campaign $record): string => config('logging.channels.papertrail.query_log') . 'Campaign:' . $record->id . ':')
                    ->openUrlInNewTab()
                    ->color('gray')
                    ->visible(fn(): bool => auth()->user()->isAdmin()),

                Action::make('Retry')
                    ->label("Re-try")
                    ->action(function (Campaign $record, RunCampaignAction $runCampaign) {
                        $record->log("Campaign retry starting for campaign: {$record->id}");
                        $runCampaign->execute($record, true);
                        return Notification::make()->success()->title('Campaign retrying')->send();
                    })->icon('heroicon-m-play')->visible(function (Campaign $record) {
                        return ($record->status != CampaignStatusEnum::DONE &&
                                $record->status != CampaignStatusEnum::IN_PROGRESS &&
                                $record->status != CampaignStatusEnum::DRAFT
                        );
                    }),

                Actions\ReplicateAction::make()->label('Clone')
                    ->beforeReplicaSaved(function (Campaign $replica, array $data) {
                        $replica->status = CampaignStatusEnum::DRAFT->value;
                        $replica->total_word_length = null;
                        $replica->required_word_length = null;
                        $replica->title = null;
                        $replica->url = null;
                        $replica->meta = null;
                        $replica->cover_image = null;

                        $ebookFormat = EbookFormat::find($replica->ebook_format_id);
                        $newEbookFormat = EbookFormat::create($ebookFormat->toArray());
                        $replica->ebook_format_id = $newEbookFormat->id;
                    })->form([
                            Wizard::make([
                                BasicInfoWizardStep::make(),
                                AudienceStyleWizardStep::make(),
                            ]),
                        ]),

                Action::make('Start')->action(function (Campaign $record, RunCampaignAction $runCampaign) {
                    $runCampaign->execute($record, true);
                    return Notification::make()->success()->title('Campaign starting!')->send();
                })->icon('heroicon-m-play')->visible(function (Campaign $record) {
                    return $record->status == CampaignStatusEnum::DRAFT;
                }),

                //                Action::make('Add Error Message')
//                                     ->label('Add Error Message')
//                                     ->color('danger')
//                                     ->form(function () {
//                                         return AddErrorForm::getForm();
//                                     })
//                                     ->action(function (Campaign $record, array $data) {
//                                         return AddErrorForm::action($record, $data);
//                                     })
//                                     ->icon('heroicon-o-exclamation-triangle')
//                                     ->visible(fn(): bool => auth()->user()->isAdmin()),

            ])->button()->label("Action")->color("warning"),

            Actions\ViewAction::make()->icon("heroicon-m-eye"),
        ];
    }
    /**
     * @throws \Exception
     */
    protected function getFormActions(): array
    {
        return [
            $this->getSaveFormAction()->icon('heroicon-o-check-circle')->color('success'),
            // $this->getCreateAnotherFormAction(),
            Action::make('save_and_generate')
                ->label('Save & Generate Ebook')
                ->icon('heroicon-o-play')
                ->color('warning')
                ->action(function ($record) {
                    $data = $this->form->getState();

                    // Separate out ebookFormat data
                    $ebookFormatData = $livewire->data['ebookFormat'] ?? [];
                    $ebookFormatData = collect($ebookFormatData)->except([
                        'id', 'created_at', 'updated_at', 'campaign_id'
                    ])->toArray();

                    // Remove keys not directly fillable in Campaign
                    unset($data['types'], $data['sample'], $data['ebookFormat']);

                    // Update the Campaign record
                    $record->update($data);

                    // Update related ebookFormat if it exists
                    if (!empty($ebookFormatData)) {
                        unset($ebookFormatData['types'], $ebookFormatData['sample']);
                        $record->ebookFormat()->update($ebookFormatData);
                    }

                    if($record->getForm("generate_ai_cover_image")){
                        GenerateCoverImage::dispatch($record->id);
                    }
                    GenerateEbookJob::dispatch($record->id);
                    // Notify the user of success
                    Notification::make()
                        ->title("Campaign saved and we're processing your ebook generation.")
                        ->success()
                        ->send();

                    $this->redirect('/campaign');
                })->visible(function (Campaign $record) {
                    return $record->status == CampaignStatusEnum::DONE;
                }),

            Action::make('save_and_start')
                  ->label('Save & Start Ebook Campaign')
                  ->icon('heroicon-o-play')
                  ->color('warning')
                  ->action(function ($record, $livewire, RunCampaignAction $runCampaign) {
                      $data = $this->form->getState();

                      // Separate out ebookFormat data
                      $ebookFormatData = $livewire->data['ebookFormat'] ?? [];
                      $ebookFormatData = collect($ebookFormatData)->except([
                          'id', 'created_at', 'updated_at', 'campaign_id'
                      ])->toArray();

                      // Remove keys not directly fillable in Campaign
                      unset($data['types'], $data['sample'], $data['ebookFormat']);

                      // Update the Campaign record
                      $record->update($data);

                      // Update related ebookFormat if it exists
                      if (!empty($ebookFormatData)) {
                          unset($ebookFormatData['types'], $ebookFormatData['sample']);
                          $record->ebookFormat()->update($ebookFormatData);
                      }

                      $runCampaign->execute($record, true);
                      Notification::make()->success()->title('Campaign starting!')->send();

                      $this->redirect('/campaign');
                  })->visible(function (Campaign $record) {
                    return $record->status == CampaignStatusEnum::DRAFT;
                }),
            $this->getCancelFormAction(),
        ];
    }
    protected function mutateFormDataBeforeSave(array $data): array
    {
        unset($data['types']);
        unset($data['sample']);
        return $data;
    }
}
