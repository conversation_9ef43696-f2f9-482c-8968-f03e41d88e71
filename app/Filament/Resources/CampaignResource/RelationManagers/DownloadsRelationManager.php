<?php

namespace App\Filament\Resources\CampaignResource\RelationManagers;

use App\Enum\FeatureEnum;
use App\Filament\Resources\CampaignResource\PublicShareForm;
use App\Models\Campaign;
use App\Models\Download;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Columns\TextColumn;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Storage;

class DownloadsRelationManager extends RelationManager
{
    protected static string $relationship = 'downloads';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
//                Forms\Components\TextInput::make('path')
//                    ->required()
//                    ->maxLength(255),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('path')
            ->columns([
                TextColumn::make('campaign.topic')
                          ->label("File version")
                          ->formatStateUsing(function ($state, $record) {
                              return str_replace(" ", "_", $state) . "_v" . $record->file_version;
                          }),
                TextColumn::make('type')
                          ->label("Type")->badge()
                          ->formatStateUsing(fn ($state) => strtoupper($state)),

                Tables\Columns\TextColumn::make('created_at')
                                         ->label('Created')
                                         ->searchable()
                                         ->sortable()
                                         ->alignCenter()
                                         ->formatStateUsing(fn(Download $record) => $record->created_at->diffForHumans()),
            ])->defaultSort("created_at", "desc")
            ->filters([
                //
            ])
            ->headerActions([
                //Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\Action::make('Download file')
                                        ->label(fn (Download $record) => $record->type === 'mp3' ? 'Download Audio Book' : 'Download File')
                                        ->icon('heroicon-o-arrow-down-tray')
                                        ->action(function (Download $record) {
                                            if (!isS3FileExist($record->path)) {
                                                Notification::make()
                                                            ->title("{$record->type} file not found.")->danger()->send();
                                            }
                                            if ($record->type === 'mp3') {
                                                $this->js(<<<JS
                                                    window.open('{$this->getTemporaryUrl($record)}', '_blank');
                                                JS);
                                                return;
                                            }
                                            return Storage::disk('s3')->download($record->path);
                                        })
                                        ->color('gray'),

                    Tables\Actions\Action::make('share_ebook')
                                         ->label("Share flipbook")
                                         ->icon('heroicon-o-globe-americas')
                                         ->form(function (Download $record) {
                                             return PublicShareForm::getForm($record);
                                         })
                                         ->action(function (Download $record, array $data) {
                                             PublicShareForm::action($record, $data);
                                         })
                                         ->color('gray')
                                         ->visible(function (Download $record) {
                                             return $record->type == 'pdf' && isCampaignTypeEnabled( FeatureEnum::LINK_SHARING->value);
                                         })
                                         ->disabled(fn() => ! auth()->user()->getUser()->hasLinkSharing()),

                    Tables\Actions\Action::make('cover_image')
                                         ->label("Download cover image")
                                         ->icon('heroicon-o-arrow-down-tray')
                                         ->action(function ($record) {
                                             $url = $this->getValidCoverImageUrl($record);

                                             if (! $url) {
                                                 Notification::make()
                                                             ->title("Ebook cover file not found.")
                                                             ->danger()
                                                             ->send();

                                                 return;
                                             }

                                             $path = $this->parseStoragePathFromUrl($url);

                                             return Storage::disk('s3')->download($path);
                                         })
                                         ->color('gray')
                                         ->visible(fn($record) => $this->getValidCoverImageUrl($record) !== null && in_array($record->type, ['pdf', 'epub'])),
                ])->iconButton(),

                //Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }




    private function getValidCoverImageUrl($record): ?string
    {
        if ($this->isValidS3FileUrl($record->cover_image)) {
            return $record->cover_image;
        }

        if ($this->isValidS3FileUrl(optional($record->campaign)->cover_image)) {
            return $record->campaign->cover_image;
        }

        return null;
    }

    private function isValidS3FileUrl(?string $url): bool
    {
        // If the URL is empty, return false
        if (blank($url)) {
            return false;
        }

        // Check if it's a valid URL
        if (filter_var($url, FILTER_VALIDATE_URL)) {
            // It's a URL, check if it's an S3 URL and cache the result
            return $this->isS3Url($url);
        }

        // It's likely an S3 path, parse and check it
        $path = $this->parseStoragePathFromUrl($url);

        // If path is null or invalid, return false
        if (blank($path)) {
            return false;
        }

        // Cache the S3 existence check for 6 hours
        return Cache::remember("s3.exists:$path", now()->addHours(6), function () use ($path) {
            return Storage::disk('s3')->exists($path); // Check if file exists in S3
        });
    }

    /**
     * Check if the URL is a valid S3 URL.
     */
    private function isS3Url(string $url): bool
    {
        // Define the base URL for your S3 domain (this will be used to check if the URL matches)
        $s3Base = 'https://aiebook.nyc3.digitaloceanspaces.com/';

        // If the URL starts with the S3 base, extract the file path
        if (str_starts_with($url, $s3Base)) {
            // Extract the S3 path from the URL
            $path = ltrim(str_replace($s3Base, '', $url), '/');

            // Cache the result for 6 hours
            return Cache::remember("s3.exists:$path", now()->addHours(6), function () use ($path) {
                return Storage::disk('s3')->exists($path); // Check if the file exists in S3
            });
        }

        // If it's not an S3 URL, return false
        return false;
    }

    /**
     * Parse the S3 file path from the URL.
     */
    private function parseStoragePathFromUrl(string $url): ?string
    {
        // Customize this according to your space domain setup
        $base = 'https://aiebook.nyc3.digitaloceanspaces.com/';

        if (str_starts_with($url, $base)) {
            // Extract the path after the base URL
            return ltrim(str_replace($base, '', $url), '/');
        }

        // If it doesn't match the base, return null (it's a path)
        return $url;  // Return the path if it's not a URL
    }
    
    protected function getTemporaryUrl(Download $record): string
    {
        return Storage::disk('s3')->temporaryUrl($record->path,
            now()->addMinutes(30)
        );
    }
}
