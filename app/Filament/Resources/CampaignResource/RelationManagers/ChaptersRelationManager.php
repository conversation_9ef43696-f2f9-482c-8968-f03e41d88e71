<?php

namespace App\Filament\Resources\CampaignResource\RelationManagers;

use App\Enum\CampaignStatusEnum;
use App\Jobs\GenerateEbookJob;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Notifications\Notification;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class ChaptersRelationManager extends RelationManager
{
    protected static string $relationship = 'chapters';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\RichEditor::make('title')
                                           ->label("Chapter title")
                                           ->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
                                           ->required()->columnSpan("full"),

                Forms\Components\RichEditor::make('intro')
                                           ->label("Chapter intro")
                                           ->required()->columnSpan("full"),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('plain_title')
            ->columns([
                Tables\Columns\TextColumn::make('title')->html(),
                Tables\Columns\TextColumn::make('status')
                                         ->badge()
                                         ->sortable()
                                         ->color(fn($state): string => $state->getColor())
                                         ->formatStateUsing(fn(CampaignStatusEnum $state): string => ucfirst($state->value)),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make()->mutateFormDataUsing(function (array $data){
                    $data["chapter_number"] = $this->ownerRecord->chapters->count() + 1;
                    $data["status"] = CampaignStatusEnum::DONE;
                    $data["chapter_total_words"] = str_word_count($data["intro"]);
                    return $data;
                }),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\Action::make('chapter')->label("Go to chapter")
                                     ->icon('heroicon-o-arrow-turn-down-right')
                                     ->url(function ($record){
                                         return "/chapters/{$record->id}/edit";
                                     })
                                     ->color('blue'),
                //Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //Tables\Actions\DeleteBulkAction::make(),
                ]),
            ]);
    }
}
