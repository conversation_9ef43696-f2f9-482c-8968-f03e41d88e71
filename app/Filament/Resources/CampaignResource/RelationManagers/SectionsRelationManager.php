<?php

namespace App\Filament\Resources\CampaignResource\RelationManagers;

use App\Enum\CampaignStatusEnum;
use Filament\Forms;
use Filament\Forms\Form;
use Filament\Resources\RelationManagers\RelationManager;
use Filament\Tables;
use Filament\Tables\Table;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\SoftDeletingScope;

class SectionsRelationManager extends RelationManager
{
    protected static string $relationship = 'sections';

    public function form(Form $form): Form
    {
        return $form
            ->schema([
                Forms\Components\RichEditor::make('title')
                                           ->label("Section title")
                                           ->extraInputAttributes(['style' => 'min-height: 5rem; max-height: 5vh; overflow-y: auto;'])
                                           ->required()->columnSpan("full"),

                Forms\Components\RichEditor::make('intro')
                                           ->label("Section intro")
                                           ->required()->columnSpan("full"),
                Forms\Components\RichEditor::make('body')
                                           ->label("Section body")
                                           ->required()->columnSpan("full"),
            ]);
    }

    public function table(Table $table): Table
    {
        return $table
            ->recordTitleAttribute('plain_title')
            ->columns([
                Tables\Columns\TextColumn::make('title')->html(),
                Tables\Columns\TextColumn::make('status')
                                         ->badge()
                                         ->sortable()
                                         ->color(fn($state): string => $state->getColor())
                                         ->formatStateUsing(fn(CampaignStatusEnum $state): string => ucfirst($state->value)),
            ])
            ->filters([
                //
            ])
            ->headerActions([
                Tables\Actions\CreateAction::make(),
            ])
            ->actions([
                Tables\Actions\EditAction::make(),
                Tables\Actions\DeleteAction::make(),
            ])
            ->bulkActions([
                Tables\Actions\BulkActionGroup::make([
                    //Tables\Actions\DeleteBulkAction::make(),
                ]),
            ])->defaultSort('title');
    }

    /**
     * @param  string|null  $title
     */
    public static function set_title(?string $title): void
    {
        self::$title = strip_tags($title);
    }
}
