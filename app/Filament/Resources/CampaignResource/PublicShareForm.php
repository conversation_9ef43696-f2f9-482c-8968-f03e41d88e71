<?php

namespace App\Filament\Resources\CampaignResource;

use App\Models\Download;
use App\Traits\EncryptHelper;
use App\Models\Campaign;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Toggle;
use Filament\Notifications\Notification;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\Actions\Action;
use LaraZeus\Qr\Components\Qr;

class PublicShareForm
{
    use EncryptHelper;

    static function getForm(Download $record): array
    {
        $hash = $record->getMeta('public_share_hash') ?: EncryptHelper::encrypt($record->id);
        $allowAccess = $record->getMeta('allow_public_access') ?? false;

        return [
            Hidden::make('hash')->default($hash),

            TextInput::make('url')
                ->label('Flipbook Share Url')
                ->default(route('share.read', array($hash)))
                ->helperText("Copy this url and share it anyone you want to share this eBook.")
                ->suffixIcon('heroicon-m-link')
                ->hintAction(
                    Action::make('generate_qr_code')
                        ->label('Generate QR Code')
                        ->icon('heroicon-m-qr-code')
                        ->fillForm(fn(Download $record) => [
                            'qr-options' => \LaraZeus\Qr\Facades\Qr::getDefaultOptions(),
                            'qr-data' => route('share.read', array($hash)),
                        ])
                        ->form(\LaraZeus\Qr\Facades\Qr::getFormSchema('qr-data', 'qr-options'))
                        ->action(fn($data) => Notification::make()->title('QR code successfully generated.')->success()->send()),
                )
                ->disabled(),

            Toggle::make('allow_public_access')->default($allowAccess),

            // Actions::make([
            //     Actions\Action::make('generate_qr_code')
            //         ->label('Generate QR Code')
            //         ->icon('heroicon-m-qr-code')
            //         ->fillForm(fn(Campaign $record) => [
            //             'qr-options' => \LaraZeus\Qr\Facades\Qr::getDefaultOptions(),
            //             'qr-data' => route('share.read', array($hash)),
            //         ])
            //         ->form(\LaraZeus\Qr\Facades\Qr::getFormSchema('qr-data', 'qr-options'))
            //         ->action(fn($data) => dd($data)),
            // ]),
        ];
    }

    static function action(Download $record, array $data): void
    {
        $meta = $record->meta ?? [];

        $meta['allow_public_access'] = $data['allow_public_access'];
        $meta['public_share_hash'] = $data['hash'] ?? '';

        $record->meta = $meta;
        $record->save();

        if ($data['allow_public_access']) {
            Notification::make()->title('Public access is now enabled.')->success()->send();
        } else {
            Notification::make()->title('Public access is now disabled.')->warning()->send();
        }
    }
}
