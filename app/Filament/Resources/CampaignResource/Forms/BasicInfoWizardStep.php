<?php

namespace App\Filament\Resources\CampaignResource\Forms;

use App\Actions\HighestRequiredCreditCalculator;
use App\Enum\CampaignTypeEnum;
use App\Enum\CreditDeductEnum;
use App\Enum\ImageSources;
use App\Enum\ImageStyles;
use App\Models\Campaign;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\Placeholder;
use Filament\Forms\Components\Radio;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Textarea;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard\Step;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Illuminate\Support\HtmlString;

class BasicInfoWizardStep
{
    public static function make()
    {
        return Step::make('Basic Information')
                          ->icon('heroicon-o-document-text')
                          ->schema([
                              Fieldset::make("Type")
                                        ->columns(1)
                                        ->schema([
                                            Radio::make('type')
                                                    ->required()
                                                    ->default(CampaignTypeEnum::DEFAULT->value)
                                                    ->options(CampaignTypeEnum::getAllowedTypes())
                                                    ->afterStateUpdated(function ($get, $set, $context) {
                                                        if ($context == 'create' && $get('type') == CampaignTypeEnum::YOUTUBE->value && !auth()->user()->youtube_api_key) {
                                                            Notification::make()->title('YouTube API Key Required')->body('Please add your YouTube API Key in your settings to create YouTube Campaign.')->danger()->send();
                                                            redirect()->away(redirectAfterNotify('/settings?highlight=youtube-api-keys'));
                                                            return;
                                                        }
                                                    })
                                                    ->disabled(fn($context) => $context == 'edit')
                                                    ->reactive(),
                                        ]),
                              TextInput::make('topic')
                                       ->label('Topic')
                                       ->required()
                                       ->placeholder('Enter the topic of your ebook')
                                       ->helperText(new HtmlString("Specify the main topic of the ebook")),

                              TextInput::make('title')
                                       ->label('Ebook Title')
                                       ->required()
                                       ->placeholder('Enter title of your ebook')
                                       ->afterStateUpdated(function ($set, $get) {
                                           $set('form.cover_image_title', $get('title'));
                                       }),

                              TextInput::make('form.content_url')
                                      ->required()
                                      ->label('Content URL')
                                      ->placeholder("https://aiwisemind.com/benefits-of-coffee")
                                      ->visible(function (Get $get){
                                          return $get("type") == CampaignTypeEnum::INFORMATIONAL_POST->value;
                                      })
                                      ->url(),

                              TextInput::make('form.youtube_url')
                                       ->required()
                                       ->label('Youtube URL')
                                       ->placeholder("https://www.youtube.com/watch?v=TBSB3BBDSlo")
                                       ->visible(function (Get $get){
                                           return $get("type") == CampaignTypeEnum::YOUTUBE->value;
                                       })
                                       ->url(),

                              Textarea::make('form.context')
                                      ->required()
                                      ->label('Context')
                                      ->visible(function (Get $get){
                                          return $get("type") == CampaignTypeEnum::DEFAULT->value;
                                      })
                                      ->placeholder('Provide a brief description or context of the ebook')
                                      ->helperText(new HtmlString("Enter a concise context/description")),

                              TextInput::make('author')
                                       ->label('Ebook Author')
                                       ->required()
                                       ->placeholder('Enter the name of your ebook author')
                                       ->default(function (){
                                           return auth()->user()->name;
                                       }),

                              Checkbox::make('form.image')
                                      ->label('Include Images')->reactive()
                                      ->helperText('Check if the ebook will contain images')
                                      ->visible(function () {
                                          return count(ImageSources::all()) > 0;
                                      }),

                              Fieldset::make('Image Sources')
                                      ->columns(1)
                                      ->visible(function ($context, $get) {
                                          return in_array($context, ['create', 'edit', 'replicate']) && $get('form.image');
                                      })
                                      ->schema([
                                          Select::make('form.image_count')
                                                // ->searchable()
                                                ->visible(fn(\Filament\Forms\Get $get
                                                ) => $get('form.image'))
                                                ->label('Image Count')
                                                ->default(ImageSources::DEFAULT_IMAGE_COUNT)
                                                ->options(function (?Campaign $record) {
                                                    return collect(range(1, 10))->mapWithKeys(fn($value) => [$value => $value])->all();
                                                }),
                                          CheckboxList::make('form.image_sources')
                                                      ->default(ImageSources::all())
                                                      ->options(function ($get) {
                                                          return ImageSources::get();
                                                      })->reactive()
                                                      ->visible(fn(\Filament\Forms\Get $get) => $get('form.image')),
                                                      
                                          Fieldset::make("AI Image Model")->schema([
                                              Radio::make('form.ai_image_model')->options(
                                                  ImageSources::aiImageModels()
                                              )->label("")->default(ImageSources::DALL_E_3)->helperText(new HtmlString("<span class='text-xs'>Important:</strong> To use GPT-Image-1 (which generates better images), you must verify your OpenAI organization. Please visit <u><a href=\"https://platform.openai.com/settings/organization/general\" target=\"_blank\">OpenAI Organization Settings</a></u> to complete verification.</span>"))
                                          ])->visible(function ($get) {
                                              return in_array(ImageSources::AI_IMAGES, $get('form.image_sources'));
                                          }),
                                          Fieldset::make("Preferred image style")->schema([
                                              Radio::make('form.image_style')->options(
                                                  ImageStyles::aiImageStyles()
                                              )->label("")->default(ImageStyles::ILLUSTRATION)
                                          ])->visible(function ($get) {
                                              return in_array(ImageSources::AI_IMAGES, $get('form.image_sources'));
                                          })

                                      ]),

                              Fieldset::make('Affiliate link')
                                      ->columns(1)
                                      ->schema([
                                          Checkbox::make('form.affiliate_link')
                                                  ->label("Use affiliate link")
                                                  ->default(false)
                                                  ->reactive(),

                                          Section::make('Footer Affiliate Link')->schema([

                                              TextInput::make("form.affiliate_link_keyword")
                                                       ->label("Keyword/URL")
                                                       ->placeholder("Enter a keyword or URL"),

                                              TextInput::make("form.affiliate_link_url")
                                                       ->url()
                                                       ->label("Affiliate Link")
                                                       ->suffixIcon('heroicon-m-globe-alt'),

                                          ])->columns(2)->visible(function (Get $get) {
                                              return $get('form.affiliate_link');
                                          }),

                                          Repeater::make('form.affiliate_links')->schema([

                                              TextInput::make("affiliate_link_keyword")
                                                       ->label("Keyword/URL")
                                                       ->placeholder("Enter a keyword or URL")
                                                       ->required(),

                                              TextInput::make("affiliate_link_url")
                                                       ->url()
                                                       ->label("Affiliate Link")
                                                       ->required()
                                                       ->suffixIcon('heroicon-m-globe-alt'),

                                          ])->columns(2)->visible(function (Get $get) {
                                              return $get('form.affiliate_link');
                                          })
                                      ]),

                              Select::make('page_length')
                              ->label('Total Pages')
                              ->default(30)
                              ->required()
                              ->options([
                                  "30" => "20-40 pages",
                                  "50" => "40-60 pages",
                                  "70" => "60-80 pages",
                                  "90" => "80-100 pages",
                                  "110" => "100-120 pages",
                                  "130" => "120-140 pages",
                                  "150" => "140-160 pages",
                                  "170" => "160-180 pages",
                              ])
                              ->reactive()
                              ->helperText(function (?string $state) {
//                                    if (!$state) return null;
//
//                                    try {
//                                        $credit = HighestRequiredCreditCalculator::getHighestRequiredCredits((int) $state);
//                                        return "This campaign may cost up to {$credit} credits.";
//                                    } catch (\Throwable $e) {
//                                        return "Unable to estimate credits.";
//                                    }
                              }),

                              Select::make('ai_model')
                                    ->label('AI Model')
                                    ->default('gpt-4o-mini')
                                    ->required()
                                    ->options(function (?Campaign $record) {
                                        return ($record?->user ?: auth()->user()->getUser())?->getAvailableAIModels();
                                    })
                                    ->visibleOn(['create', 'edit']),


                              Placeholder::make('')
                                         ->content(function (Get $get, $record) {
                                             $pageLength = $get('page_length');

                                             if (!$pageLength) return null;

                                             try {
                                                 $required = HighestRequiredCreditCalculator::getHighestRequiredCredits((int) $pageLength);
                                                 $message = "This campaign may cost up to <strong>{$required}</strong> credits.";
                                                 if($record && $record->charged){
                                                     $required = CreditDeductEnum::EBOOK_GENERATION_CREDIT->value;
                                                     $message = "Regenerate ebook will cost <strong>{$required}</strong> credits.";
                                                 }
                                                 $remaining = auth()->user()->remainCredit();

                                                 if ($remaining < $required) {
                                                     $message .= new HtmlString("<div style='color: red' class='text-sm font-semibold'>You do not have enough credits. (Available: {$remaining})</div>");
                                                 }

                                                 return new HtmlString($message);
                                             } catch (\Throwable $e) {
                                                 return new HtmlString("<span style='color: red;'>Unable to estimate credits.</span>");
                                             }
                                         })
                                         ->columnSpanFull()
                                         ->visible(function ($context) {
                                             return $context == 'create';
                                         }),
                          ]);


    }
}
