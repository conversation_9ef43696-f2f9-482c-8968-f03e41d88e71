<?php

namespace App\Filament\Resources\CampaignResource\Forms;

use App\Enum\ImageSources;
use App\Models\BackgroundImage;
use App\Models\Campaign;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\CheckboxList;
use Filament\Forms\Components\Fieldset;
use Filament\Forms\Components\FileUpload;
use Filament\Forms\Components\Hidden;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard\Step;
use App\Models\Template;
use Filament\Forms\Components\Actions\Action;
use Filament\Forms\Components\Actions;
use Filament\Forms\Components\ViewField;
use Filament\Forms\Get;
use Filament\Forms\Set;
use Illuminate\Support\Facades\Storage;

class EbookFormatWizardStep
{
    public static function make()
    {
        $templates = Template::orderByDesc('is_featured')->get();
        $default   = Template::orderByDesc('is_default')->first();

        return Step::make('Ebook Format')
                   ->icon('heroicon-o-book-open')
                   ->schema([
                       Section::make("Format your eBook")
                              ->relationship("ebookFormat")
                              ->schema([
                                  ViewField::make('sample')
                                           ->view('filament.forms.components.modal')
                                           ->viewData([
                                               "templates" => $templates,
                                               "images"    => BackgroundImage::where('is_admin', true)->orWhere('user_id', auth()->user()->getUser()->id)->select([
                                                   'src', 'id'
                                               ])->whereVisible(true)->get(),
                                            //    "background_image" => $default->image?
                                            //        Storage::disk('s3')->temporaryUrl($default->image,
                                            //            now()->addMinutes(30)) : "",
                                           ]),
                                  Select::make('font')
                                        ->label('Font')
                                        ->options([
                                            'arial'           => 'Arial',
                                            'times_new_roman' => 'Times New Roman',
                                            'calibri'         => 'Calibri',
                                        ])
                                        ->default($default->font)
                                        ->placeholder('Choose the font style'),
                                  Hidden::make('background_image')
                                        ->default($default->image),
                                  Hidden::make('background_opacity')
                                        ->default(1),
                                  Select::make('font_size')
                                        ->label('Font Size')
                                        ->options([
                                            '12' => '12 pt',
                                            '14' => '14 pt',
                                            '16' => '16 pt',
                                            '18' => '18 pt',
                                        ])
                                        ->default($default->font_size)
                                        ->placeholder('Select font size'),

                                  Select::make('heading')
                                        ->label('Heading Style')
                                        ->options([
                                            '24' => '24 pt',
                                            '26' => '26 pt',
                                            '28' => '28 pt',
                                        ])
                                        ->default($default->heading)
                                        ->placeholder('Select heading style'),

                                  Select::make('sub_heading')
                                        ->label('Subheading Style')
                                        ->options([
                                            '18' => '18 pt',
                                            '20' => '20 pt',
                                            '22' => '22 pt',
                                            '24' => '24 pt',
                                        ])
                                        ->default($default->sub_heading)
                                        ->placeholder('Select subheading style'),

                                  Select::make('line_space')
                                        ->label('Line Spacing')
                                        ->options([
                                            '1.0' => '1.0',
                                            '1.5' => '1.5',
                                            '2.0' => '2.0',
                                        ])
                                        ->default($default->line_space)
                                        ->placeholder('Select line spacing'),

                                  Select::make('paragraph_space')
                                        ->label('Paragraph Spacing')
                                        ->options([
                                            '12' => '12',
                                            '14' => '14',
                                            '16' => '16',
                                            '18' => '18',
                                        ])
                                        ->default($default->paragraph_space)
                                        ->placeholder('Select paragraph spacing'),

                                  Select::make('page_size')
                                        ->label('Page Size')
                                        ->options([
                                            'a4'     => 'A4',
                                            'letter' => 'Letter',
                                            'legal'  => 'Legal',
                                        ])
                                        ->default($default->page_size)
                                        ->placeholder('Select page size'),

                                  Fieldset::make('Margin Settings')
                                        ->schema([
                                            Select::make('margin_top')
                                                ->label('Top Margin')
                                                ->options([
                                                    '5' => 'Ultra Narrow (5px)',
                                                    '10' => 'Very Narrow (10px)',
                                                    '15' => 'Narrow (15px)',
                                                    '20' => 'Slightly Narrow (20px)',
                                                    '25' => 'Moderate (25px)',
                                                    '30' => 'Slightly Wide (30px)',
                                                    '40' => 'Wide (40px)',
                                                    '50' => 'Very Wide (50px)',
                                                    '60' => 'Extra Wide (60px)',
                                                    '80' => 'Ultra Wide (80px)',
                                                ])
                                                ->default('20')
                                                ->placeholder('Select top margin size'),

                                            Select::make('margin_bottom')
                                                ->label('Bottom Margin')
                                                ->options([
                                                    '5' => 'Ultra Narrow (5px)',
                                                    '10' => 'Very Narrow (10px)',
                                                    '15' => 'Narrow (15px)',
                                                    '20' => 'Slightly Narrow (20px)',
                                                    '25' => 'Moderate (25px)',
                                                    '30' => 'Slightly Wide (30px)',
                                                    '40' => 'Wide (40px)',
                                                    '50' => 'Very Wide (50px)',
                                                    '60' => 'Extra Wide (60px)',
                                                    '80' => 'Ultra Wide (80px)',
                                                ])
                                                ->default('20')
                                                ->placeholder('Select bottom margin size'),

                                            Select::make('margin_left')
                                                ->label('Left Margin')
                                                ->options([
                                                    '5' => 'Ultra Narrow (5px)',
                                                    '10' => 'Very Narrow (10px)',
                                                    '15' => 'Narrow (15px)',
                                                    '20' => 'Slightly Narrow (20px)',
                                                    '25' => 'Moderate (25px)',
                                                    '30' => 'Slightly Wide (30px)',
                                                    '40' => 'Wide (40px)',
                                                    '50' => 'Very Wide (50px)',
                                                    '60' => 'Extra Wide (60px)',
                                                    '80' => 'Ultra Wide (80px)',
                                                ])
                                                ->default('20')
                                                ->placeholder('Select left margin size'),

                                            Select::make('margin_right')
                                                ->label('Right Margin')
                                                ->options([
                                                    '5' => 'Ultra Narrow (5px)',
                                                    '10' => 'Very Narrow (10px)',
                                                    '15' => 'Narrow (15px)',
                                                    '20' => 'Slightly Narrow (20px)',
                                                    '25' => 'Moderate (25px)',
                                                    '30' => 'Slightly Wide (30px)',
                                                    '40' => 'Wide (40px)',
                                                    '50' => 'Very Wide (50px)',
                                                    '60' => 'Extra Wide (60px)',
                                                    '80' => 'Ultra Wide (80px)',
                                                ])
                                                ->default('20')
                                                ->placeholder('Select right margin size'),

                                            Hidden::make('margins')
                                                ->default('custom')
                                                ->dehydrateStateUsing(function ($state, $get) {
                                                    return json_encode([
                                                        'top' => (int)$get('margin_top'),
                                                        'bottom' => (int)$get('margin_bottom'),
                                                        'left' => (int)$get('margin_left'),
                                                        'right' => (int)$get('margin_right'),
                                                    ]);
                                                }),
                                        ]),

                                  Select::make('text_align')
                                        ->label('Text Alignment')
                                        ->options([
                                            'left'   => 'Left',
                                            'center' => 'Center',
                                            'right'  => 'Right',
                                        ])
                                        ->default($default->text_align)
                                        ->placeholder('Choose text alignment'),


                              ]),
                       Section::make("Cover image")->schema([
                           Checkbox::make("form.generate_ai_cover_image")
                                   ->label("Generate cover image by AI")
                                   ->default(true)
                                   ->reactive()
                                   ->visible(function (){
                                       return auth()->user()->hasGptImage1Access();
                                   }),

                           TextInput::make('form.cover_image_title')
                                    ->label('Ebook Cover Image Title')
                                    ->default(fn ($get) => $get('title'))
                                    ->placeholder('Enter title of your ebook')
                                    ->visible(fn ($get) => $get('form.generate_ai_cover_image') && auth()->user()->hasGptImage1Access()),

                           FileUpload::make('cover_image')
                                     ->disk('s3')
                                     ->directory('cover-image')
                                     ->image()
                                     ->rules(['image', 'mimes:jpg,jpeg,png,gif', 'max:5120'])
                                     ->visibility('public')
                                     ->label('Custom cover image (Optional)')
                                     ->visible(function ($get) {
                                         return ! $get("form.generate_ai_cover_image") || !auth()->user()->hasGptImage1Access();
                                     }),

                       ]),

                       Actions::make([
                           Action::make('preview')
                                 ->label('Preview Format')
                                 ->icon('heroicon-o-eye')
                                 ->button()
                                 ->color('secondary')
                                 ->extraAttributes(['class' => 'preview-button'])
                                 ->action(function (Get $get, $livewire,Set $set) {
                                    if($get('background_image')){
                                        $set('ebookFormat.background_image', $get('background_image')=="none"?"":$get('background_image'));
                                        $set('ebookFormat.background_opacity', $get('background_opacity'));
                                    }
                                     $formatData = [
                                         'topic'             => $get('topic'),
                                         'font'             => $get('ebookFormat.font'),
                                         'font_size'        => $get('ebookFormat.font_size'),
                                         'heading'          => $get('ebookFormat.heading'),
                                         'sub_heading'      => $get('ebookFormat.sub_heading'),
                                         'line_space'       => $get('ebookFormat.line_space'),
                                         'paragraph_space'  => $get('ebookFormat.paragraph_space'),
                                         'text_align'       => $get('ebookFormat.text_align'),
                                         'margins'          => $get('ebookFormat.margins'),
                                         'background_opacity' => $get('ebookFormat.background_opacity') ?? 1,
                                         'background_image' => $get('ebookFormat.background_image') ? Storage::disk('s3')->temporaryUrl($get('ebookFormat.background_image'),
                                         now()->addMinutes(30)) : "",
                                     ];
                                     $livewire->dispatch('preview', ['formatData' => $formatData]);
                                 }),
                       ]),
                   ]);


    }
}
