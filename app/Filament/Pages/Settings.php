<?php

namespace App\Filament\Pages;

use App\Enum\AIModelEnum;
use App\Models\UserAiModel;
use App\Service\YoutubeClient;
use Filament\Forms\Components\Actions\Action as FormAction;
use Filament\Forms\Components\Repeater;
use Filament\Forms\Components\TextInput;
use Filament\Forms\Components\Wizard;
use Filament\Forms\Set;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\HtmlString;
use Illuminate\Validation\ValidationException;
use App\Enum\FeatureEnum;

class Settings extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-document-text';

    protected static string $view = 'filament.pages.settings';

    protected static ?int $navigationSort = 10;

    public $openrouter_api_key;
    public $openai_api_key;
    public $has_gpt4;
    public $has_gpt_image_1;
    public $youtube_api_keys = [];
    public $additional_youtube_api_keys = [];
    public $luluAPI = [];
    protected $luluURL = 'https://api.sandbox.lulu.com';

    public function mount(): void
    {
        $this->openrouter_api_key = auth()->user()->getOpenRouterApiKey();
        $this->openai_api_key = auth()->user()->getOpenAiApiKey();
        $this->has_gpt4 = auth()->user()->hasGpt4Access();
        $this->has_gpt_image_1 = auth()->user()->hasGptImage1Access();
        $this->youtube_api_keys = auth()->user()->youtube_api_key ?? [""];
        $this->luluAPI = auth()->user()->getLuluAPI() ?? [
            "client_key" => "",
            "client_secret" => "",
            "base64_encode" => "",
        ];
    }

    private function getYoutubeApiFields(): array
    {
        $items = [];

        foreach ($this->youtube_api_keys as $key => $youtube_api_key) {
            $items[] = TextInput::make('youtube_api_keys.'.$key)
                                ->label('Youtube API Key'.($key > 0 ? ' '.($key + 1) : ''))
                                ->password()
                                ->placeholder('***********')
                                ->default($this->youtube_api_keys[$key])
                                ->reactive()
                                ->helperText(function () use ($key) {
                                    if ($key > 0) {
                                        return null;
                                    }

                                    return new HtmlString('You can find your API key in your <u><a href="https://console.developers.google.com/" target="_blank">Google Developer Console</a></u>.');
                                });
        }

        $items[] = Repeater::make('additional_youtube_api_keys')
                           ->label('')
                           ->schema([
                               TextInput::make('additional_youtube_api_keys')
                                        ->label('Additional API Key')
                                        ->password()
                                        ->placeholder('***********')
                                        ->reactive()
                                        ->helperText(new HtmlString('You can find your API key in your <u><a href="https://console.developers.google.com/" target="_blank">Google Developer Console</a></u>.')),
                           ])
                           ->defaultItems(1)
                           ->columnSpanFull()
                           ->addActionLabel('Add more Youtube APIs');

        // $items[] = Wizard::make([
        //     //
        // ])->submitAction(new HtmlString(view('components.settings-button', [
        //     'actionButton' => 'saveYoutubeSettings'
        // ])));
        return $items;
    }

    private function getLuluApiFields(): array
    {
        $items = [];

        foreach ($this->luluAPI as $key => $luluAPI) {
            $items[] = TextInput::make('luluAPI.'.$key)
                                ->label(formatLabel($key))
                                ->password()
                                ->placeholder('*******************')
                                ->default($luluAPI)
                                ->required()
                                ->helperText(function () use ($key) {
                                    if ($key == 'base64_encode') {
                                        return new HtmlString('You can find your API details in your <u><a href="https://developers.sandbox.lulu.com/user-profile/api-keys" target="_blank">Lulu Profile</a></u>.');
                                    }
                                });
        }

        return $items;
    }

    protected function getFormSchema(): array
    {
       return [
           //open ai
           Wizard::make([
               Wizard\Step::make('OpenAI Settings')
                          ->icon('heroicon-o-link')
                          ->schema([
                              TextInput::make('openai_api_key')
                                       ->label('OpenAI API Key')
                                       ->password()
                                       ->placeholder('sk-...')
                                       ->default($this->openai_api_key)
                                       ->reactive()
                                      ->hint(
                                          $this->has_gpt4 || $this->has_gpt_image_1
                                              ? 'You have ' . implode(', ', array_filter([
                                                  $this->has_gpt4 ? 'GPT-4o' : null,
                                                  $this->has_gpt_image_1 ? 'GPT-Image-1' : null,
                                              ])) . ' Access'
                                              : ''
                                      )
                                       ->helperText(new HtmlString('You can find your API key in your <u><a href="https://platform.openai.com/account/api-keys" target="_blank">OpenAI Account</a></u>. '."Make sure you have activated the <b>\"Pay As You Go\"</b> on you OpenAI account.<br><br><strong>⚠️&nbsp; Important:</strong> To use GPT-Image-1 (which generates better images), you must verify your OpenAI organization. Please visit <u><a href=\"https://platform.openai.com/settings/organization/general\" target=\"_blank\">OpenAI Organization Settings</a></u> to complete verification.")),
                          ]),
           ])->submitAction(new HtmlString(view('components.settings-button', [
               'actionButton' => 'saveOpenAISettings'
           ]))),

           //open router
           Wizard::make([
               Wizard\Step::make('OpenRouter Settings')
                          ->icon('heroicon-o-link')
                          ->schema([
                              TextInput::make('openrouter_api_key')
                                       ->label('OpenRouter API Key')->password()
                                       ->placeholder('sk-or-v1-...')
                                       ->default($this->openrouter_api_key)
                                       ->reactive()
                                       ->helperText(new HtmlString('You can find your API key in your <u><a href="https://openrouter.ai/keys" target="_blank">OpenRouter Account</a></u>. ')),
                          ]),
           ])->submitAction(new HtmlString(view('components.settings-button', [
               'actionButton' => 'saveOpenRouterSettings'
           ]))),

           Wizard::make([
               Wizard\Step::make('Youtube Settings')
                          ->icon('heroicon-o-link')
                          ->schema($this->getYoutubeApiFields()),
           ])
                 ->submitAction(new HtmlString(view('components.settings-button', [
                     'actionButton' => 'saveYoutubeSettings'
                 ]))),

           Wizard::make([
               Wizard\Step::make('Lulu API Integration')
                          ->icon('heroicon-o-link')
                          ->schema($this->getLuluApiFields()),
           ])
                 ->submitAction(new HtmlString(view('components.settings-button', [
                     'actionButton' => 'saveLuluSettings'
                 ])))
                 ->visible(isCampaignTypeEnabled( FeatureEnum::LULU_INTEGRATION->value)),
       ];
    }

    public function saveOpenAISettings(): void
    {
        if (!$this->openai_api_key) {
            UserAiModel::where("user_id", auth()->id())
                       ->where("ai_model", "openai")
                       ->delete();
            Notification::make()->title('Your OpenAI API key has been removed!')->warning()->send();
            return;
        }

        $response = Http::acceptJson()->withToken($this->openai_api_key)
                        ->get('https://api.openai.com/v1/models')
                        ->json();

        $this->has_gpt4 = collect(Arr::get($response, 'data', []))->contains('id', 'gpt-4o');

        // Try test image generation with gpt-image-1
        $this->has_gpt_image_1 = false;
        try {
            $imageResponse = Http::acceptJson()->withToken($this->openai_api_key)
                                 ->post('https://api.openai.com/v1/images/generations', [
                                     'model' => 'gpt-image-1',
                                     'prompt' => 'A test image to verify model access',
                                     'n' => 1,
                                     'size' => '1024x1024',
                                     'quality' => 'low',
                                 ])
                                 ->json();

            if (!isset($imageResponse['error'])) {
                $this->has_gpt_image_1 = true;
            }
        } catch (\Exception $e) {
            // Fail silently or log if needed
            $this->has_gpt_image_1 = false;
        }

        if ($this->has_gpt4 || Arr::get($response, 'error.code') === 'model_not_found') {
            UserAiModel::updateOrCreate(
                [
                    'user_id' => auth()->id(),
                    'ai_model' => AIModelEnum::OPEN_AI->value,
                ],
                [
                    'api_key' => $this->openai_api_key,
                    'has_gpt4' => $this->has_gpt4,
                    'has_gpt_image_1' => $this->has_gpt_image_1,
                    'quota_exceeded' => false,
                ]
            );

            Notification::make()->title('Your OpenAI API key has been saved!')->success()->send();
        } else {
            Notification::make()->title('Your OpenAI API key is incorrect!')->danger()->send();
            throw ValidationException::withMessages([
                'openai_api_key' => [
                    Arr::get($response, 'error.message') ?: 'Your OpenAI API key is incorrect!'
                ],
            ]);
        }
    }

    public function saveLuluSettings(): void
    {
        if (empty($this->luluAPI['client_key']) || empty($this->luluAPI['client_secret']) || empty($this->luluAPI['base64_encode'])) {
            Notification::make()->title( 'Please fill in all the fields!')->warning()->send();
            return;
        }

        $clientKey = $this->luluAPI['client_key'];
        $clientSecret = $this->luluAPI['client_secret'];
        $base64Encode = $this->luluAPI['base64_encode'];

        $luluURL = $this->luluURL.'/auth/realms/glasstree/protocol/openid-connect/token';

        $postBody = [
            'grant_type' => 'client_credentials',
            'client_key' => $clientKey,
            'client_secret' => $clientSecret,
        ];

        $response = Http::asForm()
                        ->withHeaders([
                            'Authorization' => $base64Encode,
                        ])
                        ->post($luluURL, $postBody)
                        ->json();

        if (!empty($response) && !isset($response['error']) ) {
            $user = auth()->user();
            $meta = $user->meta ?? [];

            $meta['luluAPI'] = [
                'client_key' => $clientKey,
                'client_secret' => $clientSecret,
                'base64_encode' => $base64Encode,
            ];

            $user->meta = $meta;
            $user->save();

            Notification::make()->title('Your Lulu API key has been saved!')->success()->send();
        } else {
            Notification::make()->title('Your Lulu API key is incorrect!')->danger()->send();
            throw ValidationException::withMessages([
                'luluAPI.client_key' => [
                    Arr::get($response, 'error_description') ?: 'Your Lulu API key is incorrect!'
                ],
            ]);
        }
    }

    //save open router
    public function saveOpenRouterSettings(): void
    {
        {
            if (!$this->openrouter_api_key) {
                UserAiModel::where("user_id", auth()->id())
                           ->where("ai_model", AIModelEnum::OPEN_ROUTER->value)
                           ->delete();
                Notification::make()->title( 'Your Open router API key has been removed!')->warning()->send();
                return;
            }

            $url = 'https://openrouter.ai/api/v1/chat/completions';
            $metaLlamaBody = [
                "model" => "meta-llama/llama-3-70b-instruct",
                "messages" => [
                    ["role" => "user", "content" => "Hello!"]
                ]
            ];

            $response = Http::acceptJson()
                            ->withToken($this->openrouter_api_key)
                            ->post($url, $metaLlamaBody)
                            ->json();

            if (Arr::get($response, 'error.code') === 401 || Arr::get($response, 'error.code') === 402) {
                Notification::make()->title('Your OpenRouter API key is incorrect!')->danger()->send();

                throw ValidationException::withMessages([
                    'openrouter_api_key' => [
                        Arr::get($response, 'error.message') ?: 'Your OpenRouter API key is incorrect!'
                    ],
                ]);
            } else {
                UserAiModel::updateOrCreate(
                    [
                        'user_id' => auth()->id(),
                        'ai_model' => AIModelEnum::OPEN_ROUTER->value,
                    ],
                    [
                        'api_key' => $this->openrouter_api_key,
                        'has_gpt4' => false,
                        'quota_exceeded' => false,
                    ]
                );

                Notification::make()->title('Your OpenRouter API key has been saved!')->success()->send();
            }
        }
    }

    public function saveYoutubeSettings(): void
    {
        $this->youtube_api_keys = array_merge($this->youtube_api_keys, collect($this->additional_youtube_api_keys)->pluck('additional_youtube_api_keys')->toArray());

        $errorResponse = [];
        foreach ($this->youtube_api_keys as $key => $youtube_api_key) {
            if (empty($youtube_api_key)) {
                YoutubeClient::clearApiKey($this->youtube_api_keys);
                Notification::make()->title( 'Your YouTube API key has been removed!')->warning()->send();
                return;
            }

            if (strlen($youtube_api_key) < 10) {
                Notification::make()->title('Your Youtube API key is incorrect!')->danger()->send();
                $errorResponse[] = [
                    'pexels_api_keys.'.$key => [
                        'Your YoutubeAPI API key is incorrect!'
                    ],
                ];
                continue;
            }

            $youtube_api_key_partial = substr($youtube_api_key, 0, 5).str_repeat('*', strlen($youtube_api_key) - 10).substr($youtube_api_key, -5);

            try {
                $response = (new YoutubeClient)->validateApiKey($youtube_api_key);

                if (!empty(Arr::get($response, 'items'))) {
                    YoutubeClient::saveApiKey($youtube_api_key);
                    Notification::make()->title('Your Youtube API key: '.$youtube_api_key_partial.' has been saved.')->success()->send();
                } else {
                    Notification::make()->title($youtube_api_key_partial.' is an invalid Youtube API key!')->danger()->send();

                    $errorResponse[] = [
                        'youtube_api_keys.'.$key => [
                            Arr::get($response, 'error.message') ?: 'Your YouTube API ('.$youtube_api_key_partial.') key is incorrect!'
                        ]
                    ];
                }

            } catch (RequestException $exception) {
                if ($exception->hasResponse()) {
                    $response = json_decode($exception->getResponse()->getBody(), true);
                    $errorMessage = Arr::get($response, 'error.message');
                    Notification::make()->title($errorMessage.' for '.$youtube_api_key)->danger()->send();

                    $errorResponse[] = [
                        'youtube_api_keys.'.$key => [
                            Arr::get($response, 'error.message') ?: 'Your YouTube API ('.$youtube_api_key_partial.') key is incorrect!'
                        ],
                    ];

                } else {
                    Notification::make()->title('Your YouTube API key ( '.$youtube_api_key_partial.') is incorrect!')->danger()->send();

                    $errorResponse[] = [
                        'youtube_api_keys.'.$key => [
                            'Your YouTube API ('.$youtube_api_key_partial.') key is incorrect!'
                        ],
                    ];
                }
            }
        }

        // show errors on specific input field
        if (!empty($errorResponse)) {
            $formattedErrorResponse = collect($errorResponse)->mapWithKeys(function ($errors, $index) {
                $key = Arr::first(array_keys($errors));
                return [$key => $errors[$key]];
            })->all();

            $this->setErrorBag($formattedErrorResponse);
        }

        $this->additional_youtube_api_keys = [];
    }
}
