<?php
use App\Service\PromptBuilder;
use Filament\Support\Colors\Color;
use App\Enum\SiteColorsEnum;
use Filament\Facades\Filament;
use App\Models\User;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Cache;

function random_colors(): array
{
    // https://material.io/design/color/the-color-system.html#tools-for-picking-colors

    return [
        '#ffc300',
        '#3DA9F3',
        '#42BCD4',
        '#CDDC39',
        '#FCEB3B',
        '#F9C10A',
        '#ea9713',
        '#db4f20',
        '#795549',
        '#9E9E9E',
        '#607D8B',
        '#319688',
        '#4CAF50',
        '#8BC34A',
        '#df3c30',
        '#c61953',
        '#9C27B0',
        '#673AB7',
        '#3F51B5',
        '#3796F3',
    ];
}

function title($title): string
{
    return (string) str($title)->slug()->replace('-', ' ')->title();
}

function defaultThemeColors()
{
    return [
        'primary' => SiteColorsEnum::Amber->value,
        'secondary' => SiteColorsEnum::Slate->value,
        'gray' => SiteColorsEnum::Slate->value,
        'success' => SiteColorsEnum::Emerald->value,
        'danger' => SiteColorsEnum::Red->value,
        'info' => SiteColorsEnum::Amber->value,
        'warning' => SiteColorsEnum::Orange->value,
    ];
}

function setThemeColors(): array
{
    $themeColors = config('site-settings')['theme_colors'] ?? defaultThemeColors();
    $defaultRgbColor = Color::all();

    return array_map(function ($themeColors) use ($defaultRgbColor) {
        return $defaultRgbColor[$themeColors] ?? $defaultRgbColor['slate'];
    }, $themeColors);
}

function isFeatureEnabled(string $featureName): bool
{
    return config('site-features')[$featureName] ?? false;
}
function isCampaignTypeEnabled(string $campaignType): bool
{
    return config('site-campaign-types')[$campaignType] ?? false;
}

function platformName(): string
{
    if (isFeatureEnabled(\App\Enum\SiteFeaturesEnum::REDDIT->value)) {
        return \App\Enum\SiteFeaturesEnum::REDDIT->value;
    } else if (isFeatureEnabled(\App\Enum\SiteFeaturesEnum::YOUTUBE->value)) {
        return \App\Enum\SiteFeaturesEnum::YOUTUBE->value;
    }

    return \App\Enum\SiteFeaturesEnum::MEDIUM->value;

}

function replaceMiddleWithAsterisks($input, $count)
{
    $length = strlen($input);
    if ($length <= $count) {
        return $input; // If the length is $count or less, just return the original string.
    }

    $middleStart = intval(($length - $count) / 2); // Calculate the start position for the $count characters to be replaced
    $middleEnd = $middleStart + $count; // Calculate the end position

    $firstPart = substr($input, 0, $middleStart); // Extract the first part of the string
    $lastPart = substr($input, $middleEnd); // Extract the last part of the string

    return $firstPart . str_repeat('*', $count) . $lastPart; // Concatenate the parts with 8 asterisks in the middle
}

if (!function_exists('prompts')) {
    /**
     * Load and return prompts from the JSON file.
     *
     * @return array
     */
    function prompts(): array
    {
        return (new PromptBuilder())->all();
    }
}

if (!function_exists('promptBuilder')) {
    /**
     * Load and return prompts from the JSON file.
     *
     * @return array
     */
    function promptBuilder(string $promptTemplate, array $data)
    {
        foreach ($data as $key => $value) {
            $promptTemplate = str_replace(":$key", $value, $promptTemplate);
        }

        return $promptTemplate;
    }
}

if (!function_exists('formatAIResponse')) {
    /**
     * Load and return prompts from the JSON file.
     *
     * @return array
     */
    function formatAIResponse($response): array
    {
        $cleanedResponse = preg_replace('/```\w*\n|\n```/', '', trim($response));
        $data = json_decode($cleanedResponse, true);
        return $data;
    }
}

if (!function_exists('formatAIResponseHtml')) {
    /**
     * Load and return prompts from the JSON file.
     *
     * @return string
     */
    function formatAIResponseHtml($response): string
    {
        $content = str_replace(["```html", "```", "\\n", "\n"], "", $response);
        return trim($content);
    }
}

if (!function_exists('placeSectionImageAfterIntro')) {
    function placeSectionImageAfterIntro(string $intro, string $imageUrl, string $title): string
    {
        //        // Validate the URL
//        if (!filter_var($imageUrl, FILTER_VALIDATE_URL)) {
//            throw new \Exception("Invalid image URL provided.");
//        }

        $altText = strip_tags($title);
        return $intro . "\n<img src=\"$imageUrl\" alt=\"$altText\">";
    }
}

function get_scrapeowl_proxy(): ?string
{
    return "http://scrapeowl:" . config('services.scrapeowl.api_key') . "@proxy.scrapeowl.com:9000";
}

function get_scrapeowl_premium_proxy($country = null): ?string
{
    if (is_null($country)) {
        return "http://scrapeowl:" . config('services.scrapeowl.api_key') . ":premium_proxies=<EMAIL>:9000";
    }

    return "http://scrapeowl:" . config('services.scrapeowl.api_key') . ":premium_proxies=true:country=" . $country . "@proxy.scrapeowl.com:9000";
}

function formatLogMessage($message)
{
    return is_string($message) ? $message : json_encode($message);
}

function formatLabel($key)
{
    return is_string($key) ? ucwords(str_replace('_', ' ', $key)) : $key;
}

function redirectAfterNotify(string $url): string
{
    return Filament::getTenant() ? url()->previous() : $url;
}

function url_add_query($url, array $query): string
{
    $parts = parse_url($url);

    if (isset($parts['query'])) {
        parse_str($parts['query'], $existingQuery);
    } else {
        $existingQuery = [];
    }

    $query = array_merge($existingQuery, $query);

    $parts['query'] = http_build_query($query);

    return build_url($parts);
}

function build_url(array $parts)
{
    return (isset($parts['scheme']) ? "{$parts['scheme']}:" : '') .
        ((isset($parts['user']) || isset($parts['host'])) ? '//' : '') .
        (isset($parts['user']) ? "{$parts['user']}" : '') .
        (isset($parts['pass']) ? ":{$parts['pass']}" : '') .
        (isset($parts['user']) ? '@' : '') .
        (isset($parts['host']) ? "{$parts['host']}" : '') .
        (isset($parts['port']) ? ":{$parts['port']}" : '') .
        (isset($parts['path']) ? "{$parts['path']}" : '') .
        (isset($parts['query']) ? "?{$parts['query']}" : '') .
        (isset($parts['fragment']) ? "#{$parts['fragment']}" : '');
}

function explode_targets($targets)
{
    return str($targets)->explode(PHP_EOL)->flatten()->map(fn($keyword) => trim($keyword))->filter();
}

function ebookPlan($key)
{
    // Load the JSON file
    $jsonPath = resource_path('ebook_plan/ebook_plan.json');

    // Check if the file exists
    if (!file_exists($jsonPath)) {
        throw new \Exception("The ebook plan file does not exist.");
    }

    $jsonData = file_get_contents($jsonPath);
    $ebookPlans = json_decode($jsonData, true);

    // Check if the key exists in the array
    if (array_key_exists($key, $ebookPlans)) {
        return $ebookPlans[$key];
    }

    // Return null or a default value if the key is not found
    return [
        "total_words" => 7972,
        "total_chapters" => 6,
        "words_per_chapter_range" => 1328,
        "sections_per_chapter_range" => 3,
        "words_per_section_range" => 442,
        "key_points_per_section_range" => 3,
        "words_per_key_point_range" => 145
    ];
}

function get_residential_proxies()
{
    // authorized ips ************* - app.aiwisemind.com
    return [
        "epimetheus.p.shifter.io:11170",
        "epimetheus.p.shifter.io:11171",
        "epimetheus.p.shifter.io:11172",
        "epimetheus.p.shifter.io:11173",
        "epimetheus.p.shifter.io:11174",
    ];
}

function getBulkSelectedRecord($className, $records, $livewire)
{
    if (count($records) <= 0) {
        $selectedIds = $livewire?->selectedTableRecords;
        return $className::whereIn('id', $selectedIds)->get();
    }
    return $records;
}

function generateNameFromEmail(string $email): string
{
    $counter = 1;
    $name = strstr($email, '@', true);
    $slug = ucwords(str_replace(['.', '_'], ' ', $name));

    while (User::where('slug', $slug)->exists()) {
        $slug = $slug . '-' . $counter;
        $counter++;
    }
    return $slug;
}

if (!function_exists('isS3FileExist')) {
    function isS3FileExist(string $path, int $ttlInSeconds = 604800): bool
    {
        $cacheKey = $path;

        return Cache::remember($cacheKey, $ttlInSeconds, function () use ($path) {
            return Storage::disk('s3')->exists($path);
        });
    }
}

if (!function_exists('cleanAiHtml')) {
    function cleanAiHtml($html) {
        // Decode JSON-escaped characters
        $html = json_decode('"' . trim($html) . '"');

        // Use DOMDocument to fix broken/malformed tags
        libxml_use_internal_errors(true);
        $doc = new DOMDocument();
        $doc->loadHTML(mb_convert_encoding($html, 'HTML-ENTITIES', 'UTF-8'));
        libxml_clear_errors();

        $cleanHtml = $doc->saveHTML($doc->getElementsByTagName('body')->item(0));
        return preg_replace('/^<body>|<\/body>$/', '', trim($cleanHtml));
    }
}

if (!function_exists('linkifyContent')) {
    function linkifyContent(?string $content, $ebook): string
    {
        if (empty($content)) {
            return '';
        }

        $links = $ebook->getAffiliateLinks();

        // This regex matches content inside <h1> to <h6> tags (inclusive) or other content.
        $pattern = '/(<h[1-6][^>]*>.*?<\/h[1-6]>)/is';

        // Split content into heading and non-heading parts
        $parts = preg_split($pattern, $content, -1, PREG_SPLIT_DELIM_CAPTURE);

        foreach ($parts as &$part) {
            // Skip if this is a heading tag
            if (preg_match('/^<h[1-6][^>]*>.*<\/h[1-6]>$/is', $part)) {
                continue;
            }

            // Apply linkification only on non-heading parts
            foreach ($links as $keyword => $url) {
                $escapedKeyword = preg_quote($keyword, '/');
                $regex = '/\b(' . $escapedKeyword . ')\b/iu';

                $part = preg_replace_callback($regex, function ($matches) use ($url) {
                    $matchedText = $matches[1];
                    return '<a href="' . e($url) . '" target="_blank">' . $matchedText . '</a>';
                }, $part, 1); // Replace only the first occurrence
            }
        }

        // Rejoin all parts
        return implode('', $parts);
    }
}

if (!function_exists('imageFileName')) {
    function imageFileName(?string $fileName): string
    {
        if (empty($fileName)) {
            return '';
        }

        $fileName = strip_tags($fileName);
        $safeFilename = preg_replace('/[^A-Za-z0-9_\-\.]/', '_', $fileName);

        $safeFilename = strtolower($safeFilename);
        $safeFilename = preg_replace('/_+/', '_', $safeFilename);
        $safeFilename = trim($safeFilename, '_');

        return $safeFilename;
    }
}








