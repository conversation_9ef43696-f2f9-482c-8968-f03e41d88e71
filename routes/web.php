<?php

use App\Enum\ImageSources;
use App\Http\Controllers\AIImageGeneratorController;
use App\Http\Controllers\BulkDownloadController;
use App\Http\Controllers\PublicShareController;
use App\Models\Campaign;
use App\Service\GenerateEbookAsPDF;
use App\Service\ImageGenerateFactory;
use App\Service\MediumService;
use App\Service\YoutubeClient;
use Illuminate\Support\Facades\Route;
use App\Service\AIModel\Contracts\AIClientInterface;
use Illuminate\Support\Facades\Storage;

/*
|--------------------------------------------------------------------------
| Web Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::redirect('/', '/login')->name('login');

Route::get('/ticket/{ticket}', function (\App\Models\Ticket $ticket) {
    return redirect()->to("/tickets/{$ticket->id}".(auth()->user()->canUpdateTicket() ? '/edit' : ''));
})->name('ticket.show')->middleware('auth');

Route::get('/preview/{campaign}', function (\App\Models\Campaign $campaign) {
    $ebook=$campaign;
    $format = $ebook->ebookFormat;
    return view('ebook.ebook-pdf', compact('ebook','format'));
});
Route::get('/image/{campaign}', function (\App\Models\Campaign $campaign) {
    $imageUrl = ImageGenerateFactory::create("google_search_images")->generateImage($campaign, $campaign->sections->first());
    return response()->json([
        "url"=>$imageUrl
    ]);
});

Route::post('/generate-image',[AIImageGeneratorController::class,'generateImage'])->name('generate.image');

Route::get('/content/csv/download', [BulkDownloadController::class, 'downloadCSV'])->name('content.csv.download');
Route::get('/share/{hash}', PublicShareController::class)->name('share.read');

// Review routes
Route::post('/reviews', [\App\Http\Controllers\ReviewController::class, 'store'])->name('reviews.store');
Route::get('/campaign_reviews/{campaign}/download', [BulkDownloadController::class, 'downloadReviews'])->name('reviews.download');